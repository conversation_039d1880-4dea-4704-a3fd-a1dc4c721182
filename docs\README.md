# 监测项目- 前端开发概述

## 运维部署

## 技术选型

目录结构

```dir
.
├── README.md                   (项目描述)
├── docs                        (技术文档)
├── config                      (配置)
├── components                  (组件)
├── utils                       (方法)
├── hooks                       (hooks)
├── scripts                     (脚本)
├── style                       (样式)
├── test                        (用例)
├── dist                        (产物)
│   │                                       ----主包为公共依赖
├── packages
│   ├── web-admin               (后台管理)
│   │   ├── public              (静态文件)
│   │   │
│   │   ├── ...                 ci配置
│   │   │
│   │   ├── src
│   │   │   ├── views           页面
│   │   │   ├── router          路由
│   │   │   ├── store
│   │   │   ├── components
│   │   │   └── main.js
│   │   ├── package.json
│   │   └── vite.config.js
│   │
│   │
│   ├── web-acquisition         (采集系统)
│   │    ├── src
│   │    ├── ...
│   │    │   └── main.js
│   │    ├── package.json
│   │    └── vite.config.js
│   ├── web-sample   z          (抽样系统)
│   │   ├── src
│   │   ├── ...
│   │   │   └── main.js
│   │   ├── package.json
│   │   └── vite.config.js
│   └── web-question            (题库系统)
│       ├── src
│       ├── ...
│       │   └── main.js
│       ├── package.json
│       └── vite.config.js
├── package.json
├── pnpm-workspace.yaml
└── eslint.config.js
```

## 包管理

采用 [pnpm](https://www.pnpm.cn/) 作为包管理

- 为所有 package 的公共依赖

`pnpm install axios -w`

- 为所有 package 的开发依赖

`pnpm install vite -wD`

- 给某个package单独安装指定依赖

`pnpm add axios --filter pgkname`

- pkg1 中将 pkg2 作为依赖进行安装

`pnpm install pgk2 -r --filter pkg1`

## 开发文档

> 采用 [VitePress](https://vitejs.cn/vitepress/) 搭建

- 运行

`pnpm run dev:docs`

- 部署

`pnpm run build:docs`

## 资源管理

## 解决方案
