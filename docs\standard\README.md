#

## 特殊注释

- 代码中特殊注释——TODO、FIXME、XXX、HACK

TODO：英语翻译为待办事项，备忘录。如果代码中有该标识，说明在标识处有功能代码待编写，待实现的功能在说明中会简略说明。

FIXME：可以拆成短语，fix me ，意为修理我。如果代码中有该标识，说明标识处代码需要修正，甚至代码是错误的，不能工作，需要修复，如何修正会在说明中简略说明。

XXX：如果代码中有该标识，说明标识处代码虽然实现了功能，但是实现的方法有待商榷，希望将来能改进，要改进的地方会在说明中简略说明。

HACK：英语翻译为砍。如果代码中有该标识，说明标识处代码我们需要根据自己的需求去调整程序代码。

- 文件头部注释

## 提交规范

- [遵循 Angular 提交规范](https://github.com/leoforfree/cz-customizable)

提交格式如下：包含了页眉（header）、正文（body）和页脚（footer）三部分

Body 部分是对本次 commit 的详细描述，可以分成多行

Footer用来关闭 Issue或以BREAKING CHANGE开头，后面是对变动的描述、 以及变动理由和迁移方法

```sh
git commit -m "feat: add landing page"
```

`feat`为类型字段用于说明 commit 影响的范围

`add landing page`目的的简短描述，不超过50个字符

| 类型字段 |                   描述                    |
| :------: | :---------------------------------------: |
|   feat   |            一个新功能 ，新特性            |
|   fix    |                 故障修复                  |
|   perf   |            改进性能的代码修改             |
|    ci    |         对CI配置文件和脚本的更改          |
|  style   |    代码格式/风格修改，注意不是css修改     |
|   docs   |               只修改了文档                |
|   test   |               测试用例修改                |
| refactor | 代码的修改并没有修改bug，也没有添加新功能 |
