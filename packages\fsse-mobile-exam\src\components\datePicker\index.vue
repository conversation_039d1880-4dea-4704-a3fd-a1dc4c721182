<template>
	<view :class="(value || state.date)? 'fa-date-month-edit' : 'fa-date-month'">		
		<picker :mode="mode" :fields="fields" :value="value || state.date" :start="startDate" :end="endDate"
				@change="bindPickerChange">
			<view class="fa-date-content">{{ value || state.date}}</view>
		</picker>
	</view>
</template>

<script setup>
import { reactive, computed, watch } from "vue"

const emit = defineEmits(['change','update:value'])

const props = defineProps({
	fields: {
		type: String,
		default: 'month' //year //month //day
	},
	value:{
		type:String,
		default:''
	},
	mode:{
		type:String,
		default:'date'
	}
})

const getDate = (type) => {
	const date = new Date();
	let year = date.getFullYear();
	let month = date.getMonth() + 1;
	let day = date.getDate();

	if (type === 'start') {
		year = year - 60;
	} else if (type === 'end') {
		year = year + 2;
	}
	month = month > 9 ? month : '0' + month;
	day = day > 9 ? day : '0' + day;
	return `${year}-${month}-${day}`;
}

const currentDate = getDate({ format: true })


const startDate = computed(() => {
	return getDate('start');
})
const endDate = computed(() => {
	return getDate('end');
})

const state = reactive({
	date: ``
})

const bindPickerChange = (e) => {
	// console.log('picker发送选择改变，携带值为', e.detail.value)
	state.date = e.detail.value
	emit('update:value',e.detail.value)
	emit('change',e.detail.value)
	
}
</script>

<style lang="scss" scoped>
	.fa-date-month{
		position: relative;
		overflow: hidden;
		height: 26px;
		line-height: 26px;
		border: 1px solid #e3e3e3;
		border-radius: 4px;
		cursor: pointer;	
		margin: 0 4px;
		display: inline-block;
		top: 9px;
	}
	.fa-date-content{
		padding: 0 20px;
		display: inline-block;		
		min-width: 60px;		
		background: #fff;	
		color: #666;	
		text-align: center;	
	}

	.fa-date-month-edit{
		position: relative;     
		height: 28px;
		line-height: 28px;
		margin-bottom: 4px;
		.fa-date-content{
			margin-left: 20px;
			padding-left: 0;	
					
		}

        &::before{
            content: "【";
            display: inline;
            margin-right: 4px;
            color: #e3e3e3;
            position: absolute;
            left: -2px;            
            top: 1px;
        }
        &::after{
            content: "】";
            display: inline;
            margin-left: 4px;
            color: #e3e3e3;
            position: absolute;
            right: -2px;            
            top: 1px;
        }
	}
</style>