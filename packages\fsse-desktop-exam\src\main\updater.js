
/**
 * 应用程序自动更新模块
 * 该模块使用electron-updater库实现桌面应用的自动更新功能
 * 包括检查更新、下载更新和安装更新的完整流程
 */
import { ipcMain, app } from 'electron' // 导入Electron的主进程模块和应用程序模块
import { autoUpdater } from 'electron-updater' // 导入自动更新模块
import path from 'path' // 导入路径处理模块

/**
 * 安装更新处理器
 * 当渲染进程请求安装更新时，退出应用并安装已下载的更新
 */
ipcMain.handle('update-version', (e) => {
  autoUpdater.quitAndInstall()
})

/**
 * 下载更新处理器
 * 当渲染进程请求下载更新时，开始下载更新包
 */
ipcMain.handle('update-downloaded', (e) => {
  autoUpdater.downloadUpdate()
})

/**
 * 更新管理类
 * 负责应用程序的全量升级流程控制
 */
export class Updater {
  /**
   * 检查更新方法
   * @param {Function} cb - 回调函数，用于向渲染进程通知更新状态和进度
   */
  static check(cb) {
    //设置是否自动下载
    autoUpdater.autoDownload = true // 启用自动下载更新
    autoUpdater.autoInstallOnAppQuit = true // 应用退出时自动安装更新

    // 开发环境特殊处理，模拟打包环境以测试更新功能
    if (process.env.NODE_ENV === 'development') {
      // 重写app.isPackaged属性，使autoUpdater认为应用已打包
      Object.defineProperty(app, 'isPackaged', {
        get() {
          return true
        },
      })
      // 设置开发环境下的更新配置文件路径
      autoUpdater.updateConfigPath = path.join(
        __dirname,
        '../dev-app-update.yml'
      )
      // console.log(autoUpdater)
    }

    // 检查更新
    console.log(`开始检查更新`)
    // 发起更新检查请求，并捕获可能的网络错误
    autoUpdater.checkForUpdates().catch((err) => {
      console.log('网络连接问题', err)
    })
    // 当 autoUpdater.checkForUpdates() 方法执行时，
    // 应用会先请求 release目录下 这个 yml 文件，得到文件里的内容后，再拿此文件中的版本号与当前版本号对比，
    // 如果此文件中的版本号比当前版本号新，则下载新版本，否则就退出更新逻辑。

    /**
     * 更新下载完成事件监听
     * 当新版本下载完成后触发，准备安装更新
     */
    autoUpdater.on('update-downloaded', async (data) => {
      // 延迟3秒后退出应用并安装更新
      // 参数说明：isSilent=true (静默安装), isForceRunAfter=true (安装后强制运行应用)
      setTimeout(() => {
        autoUpdater.quitAndInstall(true, true)
      }, 3000)

      // 通知渲染进程更新已下载完成
      cb({
        type: 'updateDownloadedEnd',
        data,
      })

      //当新版本安装包下载完成后，electron-updater 会验证文件的 sha512 值是否合法，yml 文件中包含新版本安装包的 sha512 值，electron-updater 首先计算出下载的新版本安装包的 sha512 值，然后再与 yml 文件中的 sha512 值对比，两个值相等，则验证通过，不相等则验证不通过。
      //验证通过后 Electron 则使用 Node.js 的 child-process 模块启动这个新的安装文件，以完成应用程序升级工作。
    })

    /**
     * 下载进度事件监听
     * 在更新下载过程中定期触发，提供下载进度信息
     */
    autoUpdater.on('download-progress', (data) => {
      console.log('下载监听', data)
      // 向渲染进程发送下载进度信息
      cb({
        type: 'updateProgress',
        data, // 包含下载百分比、下载速度等信息
      })
    })

    /**
     * 无可用更新事件监听
     * 当检查更新后发现当前已是最新版本时触发
     */
    autoUpdater.on('update-not-available', (res) => {
      console.log(`没有可更新版本`, res)
    })

    /**
     * 发现可用更新事件监听
     * 当检查到有新版本可用时触发
     */
    autoUpdater.on('update-available', (data) => {
      console.log('发现新版本', data)
      // 通知渲染进程有新版本可用
      cb({
        type: 'updateAvailable',
        data, // 包含新版本信息，如版本号、发布日期等
      })
    })

    /**
     * 错误事件监听
     * 当更新过程中发生错误时触发
     */
    autoUpdater.on('error', function (error) {
      console.log('出错', error)
    })
  }
}
