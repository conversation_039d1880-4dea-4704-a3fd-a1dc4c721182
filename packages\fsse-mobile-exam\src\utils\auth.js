

export function getToken() {
    return uni.getStorageSync('fsse-mobile-token')
}

export function setToken(token) {
    uni.setStorageSync('fsse-mobile-token', token)
}

export function removeToken() {
    uni.removeStorageSync('fsse-mobile-token')
}

export function toLogin() {
    // uni.redirectTo({
    //     url: "/pages/login/index"
    // })
}

// export function isLogin() {
//     return getToken() != '' && getToken() != null && getToken() != undefined
// }