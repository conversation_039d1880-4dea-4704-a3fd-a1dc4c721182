<template>
  <view class="mm-container" :id="data.quesCode">
    <view class="title" :style="properties.quesContentFontStyles">
			<text class="is-required" v-if="data.required" style="padding-top:3px;">
				<span>*</span>
			</text>
			<text class="idx">{{ data.quesNo }}.</text>
			<view >
        <view style="display: inline-block;" v-html="data.title"></view> 
        <text class="check-tip"> {{tip}}</text>       
      </view>      
		</view>

    <template v-if="data.fsseQuesFiles.length">
      <view class="file-list" v-for="(item, idx) in data.fsseQuesFiles" :key="idx">		
          <free-audio :title="item.fileName" :audioId="item.id || item.fileUrl" :url='item.fileUrl'></free-audio>
      </view>
    </template>

    <view class="ms-content">
      <view class="header">
        <view class="row">
          <view class="col" v-for="(item,idx) in options" :key="idx" >{{item.optionContent}}</view> 
        </view>
      </view>
      <!--  -->
      <view class="body">
        <view class="list">
          <view class="item" v-for="(item,idx) in data.subQues" :key="idx">
            <view class="th" :style="properties.optionFontStyles">{{item.title}}</view>
            <checkbox-group class="tr" @change="(e)=>checkboxChange(e,item,idx)">
              <label class="td" v-for="(citem,jdx) in item.options" :key="jdx">
                <checkbox :value="citem.optionNo" :checked="state.checkedMap[item.quesCode]?.includes(citem.optionNo)" :activeBackgroundColor="themeColor" borderColor="#DCDFE6" color="#fff"
                  :activeBorderColor="themeColor" backgroundColor="#fff" style="transform: scale(0.7)" />
              </label>
            </checkbox-group>
          </view>
        </view>
      </view>     
    </view>
    
  </view>
</template>

<script setup>
import {reactive,nextTick, watch, computed} from "vue"
import FreeAudio from '@/components/freeAudio'
const themeColor = getApp().globalData.themeColor;
const emit  = defineEmits(['change'])
const props = defineProps({
  data: {
    type: Object,
    default: () => { },
  },
  properties: {
    type: Object,
    default: () => { },
  },
});

const state = reactive({
  checkedMap:{},  
  exclusiveMap:{}
})



const options = computed(()=>{
  const slotList = props.data.options.filter(i=>i.optionProperties.exclusive).map(i=>{
    return {
      optionContent:i.optionContent,
      sort:i.sort
    }
  })
  props.data.subQues.forEach(i=>{
    const arr =  i.options.map(j=>{
      const isExclusive = slotList.find((i,k)=>(i.optionContent == j.optionContent) && j.sort==k+1)      
      return  isExclusive ? j.optionNo : null
    }).filter(i=>i) 
    state.exclusiveMap[i.quesCode] = arr
  })
  console.log(state.exclusiveMap)
  //  
  return props.data.options
})

const tip = computed(()=>{
  if(props.data.minLength==0 && props.data.maxLength==0){
    return ``
  }    
  return `【请选择${props.data.minLength}-${props.data.maxLength}项】`  
})

const verifyCheckExclus = (arr1,arr2)=>{
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const diff = [];
    for (const item of arr1) {
        if (!set2.has(item)) {
            diff.push(item);
        }
    }
    return diff
}

const checkboxChange = (evt,item,idx)=>{  
  const arr = evt.detail.value
  const currentSet = new Set(state.checkedMap[item.quesCode] ?? []);
  const diff = arr.filter(item => !currentSet.has(item));

  state.checkedMap[item.quesCode] = arr

  // 1. 如果当前题存在互斥，并且被选中，只留当前选中的。  
  if(state.exclusiveMap[item.quesCode] && state.exclusiveMap[item.quesCode].length && diff.length){           
    if(state.exclusiveMap[item.quesCode].includes(diff[0])){
      state.checkedMap[item.quesCode] = [diff[0]]
      return 
    }
  }  
  // 2. 如果已经选择了互斥的，之后选择不是互斥的，则留之后选择的
  // 检查当前选中的是否存在互斥，返回不是互斥的
  const ndiff =  verifyCheckExclus(arr,state.exclusiveMap[item.quesCode])     
  if(ndiff.length){
    // console.log('检查当前选中的是否存在互斥',ndiff)          
    state.checkedMap[item.quesCode] = [...ndiff]  
  }

  // 1. 如果有最多选多少限制,超出则提示，新选的去掉
  const maxLength = props.data.maxLength
  const minLength = props.data.minLength  
  if(maxLength>0 && state.checkedMap[item.quesCode]?.length > maxLength){   
    console.log('超出限制做多只能选择',maxLength)   
    uni.showToast({
      title: `此题最多只能选择${maxLength}项`,
      icon:"none",
      duration: 2000,
      success(){
        nextTick(()=>{
          state.checkedMap[item.quesCode] = state.checkedMap[item.quesCode].filter(i=>i!=diff[0])
        })
      }
    });    
  } 
}

watch(()=>state.checkedMap,(map)=>{  
  const obj = {}
  for(let quesCode in map){
    obj[quesCode] = [{
        optionNo:map[quesCode],        
      }]
  }
  emit('change',obj)  
},{
  deep:true
})


defineExpose({
  quesCode:props.data.quesCode,
  subQues:props.data.subQues.map(i=>i.quesCode),
  quesTypeCode:props.data.quesTypeCode
})

</script>

<style scoped>
/* MM 组件样式 */
.mm-container {
  
  .title {
		display: flex;	
		padding-bottom: 12px;
	}
  .idx{
    padding-top:3px;
  }

  .check-tip{
    font-size: 15px;
    color: #999999;
  }
  .ms-content{
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
  }

  .header {
    background: #f1f1f1;
    padding: 8px 11px;

    .row {
      display: flex;
    }

    .col {
      flex: 1;
    }
  }

  .body {
    .list {
      padding-top: 12px;
    }

    .item {
      .th {
        padding-left: 12px;
      }

      .tr {
        display: flex;
        align-items: center;
        padding-left: 12px;

        .td {
          flex: 1;
          padding: 12px 0;
        }
      }
    }
  }
}
</style>