/**
 * 通用的获取URL参数函数
 * 支持hash模式和history模式的URL参数解析
 * @param {string} url - 要解析的URL，如果不传则使用当前页面URL
 * @param {string} paramName - 要获取的参数名，如果不传则返回所有参数对象
 * @returns {string|object|null} 返回参数值、参数对象或null
 */
export function getTargetUrlParams(url = window.location.href, paramName = null) {
  try {
    // 如果传入的是相对路径或不完整URL，补充协议和域名
    if (!url.includes('://')) {
      url = 'http://example.com' + (url.startsWith('/') ? '' : '/') + url;
    }
    const urlObj = new URL(url);
    const params = {};
    
    // 解析query参数 (?后面的参数)
    urlObj.searchParams.forEach((value, key) => {
      params[key] = decodeURIComponent(value);
    });
    
    // 解析hash中的参数
    const hash = urlObj.hash;
    if (hash) {
      // 处理hash路由中的参数，支持多种格式：
      // 1. #/path?param=value
      // 2. #path?param=value  
      // 3. #?param=value
      // 4. #param=value&param2=value2
      
      let hashParams = '';
      
      // 检查hash中是否包含?
      if (hash.includes('?')) {
        hashParams = hash.split('?')[1];
      } else {
        // 如果hash不包含?，但包含=，则整个hash可能就是参数
        if (hash.includes('=') && !hash.includes('/')) {
          hashParams = hash.substring(1); // 去掉#号
        }
      }
      
      // 解析hash中的参数
      if (hashParams) {
        // 移除可能的fragment部分（#后面的部分）
        hashParams = hashParams.split('#')[0];
        
        const hashSearchParams = new URLSearchParams(hashParams);
        hashSearchParams.forEach((value, key) => {
          // hash参数优先级更高，会覆盖query参数
          params[key] = decodeURIComponent(value);
        });
      }
    }
    
    // 如果指定了参数名，返回对应的值
    if (paramName) {
      return params.hasOwnProperty(paramName) ? params[paramName] : null;
    }
    
    // 返回所有参数对象
    return params;
    
  } catch (error) {
    console.error('解析URL参数时发生错误:', error);
    return paramName ? null : {};
  }
}