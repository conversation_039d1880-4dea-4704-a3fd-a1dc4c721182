/**
 * 防抖函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行（true 表示立即执行，false 表示等待后执行）
 * @returns {Function} - 返回一个防抖后的函数
 */
export function debounce(func, wait = 300, immediate = false) {
    let timeout = null;

    return function (...args) {
        const context = this;

        // 如果 timeout 存在，清除定时器
        if (timeout) {
            clearTimeout(timeout);
        }

        // 立即执行模式
        if (immediate) {
            // 如果 timeout 为 null，表示可以立即执行
            const callNow = !timeout;
            timeout = setTimeout(() => {
                timeout = null;
            }, wait);
            if (callNow) {
                func.apply(context, args);
            }
        } else {
            // 非立即执行模式
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        }
    };
}

/**
 * 将对象转换为 URL 的 query 参数字符串
 * @param {Object} obj - 需要转换的对象
 * @returns {string} - 返回 query 参数字符串
 */
export function objectToQueryString(obj) {
    if (!obj || typeof obj !== 'object') {
      return '';
    }
  
    // 使用 URLSearchParams 构建查询参数
    const params = new URLSearchParams();
  
    // 递归处理嵌套对象
    function encode(key, value) {
      if (typeof value === 'object' && value !== null) {
        // 如果是对象或数组，递归处理
        for (const subKey in value) {
          if (value.hasOwnProperty(subKey)) {
            encode(`${key}[${subKey}]`, value[subKey]);
          }
        }
      } else {
        // 普通值直接添加到 URLSearchParams
        params.append(key, value);
      }
    }
  
    // 遍历对象的键值对
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        encode(key, obj[key]);
      }
    }
  
    // 返回 query 字符串
    return params.toString();
  }


  export const parseConfig = data => {
    if (Array.isArray(data)) {
      return data.map(item => parseConfig(item));
    }
    if (data.quesProperties?.config) {
      data.config = JSON.parse(data.quesProperties.config);
    }
  
    if (data.subQues?.length) {
      data.subQues.forEach(child => parseConfig(child));
    }
    return data;
  };