
/*
 * @Description: 网络请求 
 * @Date: 2024-10-09 16:39:37
 * @LastEditors: lhq
 * @LastEditTime: 2024-11-07 11:44:38
 * @FilePath: \code\校壹通\call-connect-wxprogram\src\utils\request.js
 * 基于uniapp api封装  https://uniapp.dcloud.net.cn/api/request/request.html
 */
import { nextTick } from "vue"
import { getToken, toLogin } from '@/utils/auth'

let time = null
const errorHandle = (code, message) => {
    uni.hideLoading()
    clearTimeout(time)
    time = setTimeout(() => {
        switch (code) {
            case 400:
                uni.showToast({
                    title: message,
                    icon: "error",
                    duration: 3000,
                })
                break;
            case 401:
                uni.showToast({
                    title: message,
                    icon: "none",
                    duration: 3000,
                    success() {
                        setTimeout(() => {
                            toLogin()
                        }, 2000)
                    }
                })
                break;
            case 403:
                uni.showToast({
                    title: message || '权限不足',
                    icon: "none",
                    duration: 3000
                })
                break;
            case 404:
                uni.showToast({
                    title: `资源不存在：${message}`,
                    icon: "none",
                    duration: 3000
                })
                break;
            case 500:
                if (message.length > 40) {
                    message = '请联系管理员！'
                }
                uni.showToast({
                    title:  message || `系统异常：${message || '请联系管理员！'}`,
                    icon: "none",
                    duration: 3000
                })
                break;
            case 503:
                uni.showToast({
                    title: message || `系统开小差：${message || '请稍后再试！'}`,
                    icon: "none",
                    duration: 3000
                })
                break;
            default:
                uni.showToast({
                    title: message,
                    icon: "none",
                    duration: 3000,
                })

                break;
        }
    }, 500)
    
}




const defaultConfig = {
    timeout: 3000,
    // withCredentials: true,
    header: {
        'Content-Type': 'application/json',
    },
}
/**
 * @description: 网络请求
 * @param {Object} options
 * @return {Promise}
 */
const http = (options = {}) => {
    return uni.request({ ...defaultConfig, ...options })
}

http.get = (url, options, params = {}) => {
    return uni.request({ ...defaultConfig, url, method: "GET", data: options, ...params })
}
http.post = (url, options = {}, params = {}) => {
    const config = { ...defaultConfig, url, method: "POST", data: options, ...params }
    return uni.request(config)
}

http.uploadFile = (url, options, progress) => {
    return new Promise((resolve, reject) => {
        const config = {
            url,
            ...options,
            success: (uploadFileRes) => {
                const data = uploadFileRes.data
                if (typeof data == 'string') {
                    try {
                        try {
                            const upData = JSON.parse(data)
                            resolve(upData)
                        } catch (error) {
                            reject(err)
                        }                        
                    } catch (error) {
                        reject(err)
                    }
                    return
                }
                resolve(uploadFileRes.data)
            },
            fail(err) {
                reject(err)
            }
        }
        const uploadTask = uni.uploadFile(config)
        uploadTask.onProgressUpdate((res) => {
            progress && progress(res.progress)
        });
    })
}

http.downloadFile = (url, options) => {
    return uni.downloadFile({ ...defaultConfig, url, ...options })
}

// 拦截器
const methodsList = ['request', 'uploadFile']
const interceptor = {
    invoke(args) {
        const url = import.meta.env.VITE_BASE_URL + args.url
        args.url = url
        const token = getToken()
        if (token) {
            if (args.header == undefined) args.header = {}
            args.header['X-Questionnaire-Token'] = token
        }
        
    },
    success(args) {        
        // 请求成功后，修改code值为1        
        if (args.statusCode == 200) { 
            // debugger
            if (args.data.code != 0) {    
                errorHandle(args.data.code, args.data.message || args.data.error || args.data.msg)
                args.data = args.data
                return
            }
            args.data = args.data?.data            
        } else {            
            errorHandle(args.statusCode, args.data?.message || args.data?.error || args.data?.msg)
        }


    },
    fail(err) {
        const { errMsg } = err
        if (errMsg == 'request:fail timeout') {
            uni.showToast({
                title: `请求超时，请稍后再试！`,
                icon: "none",
            })
        }
    },
    complete(res) {
    }
}

methodsList.forEach(method => {
    uni.addInterceptor(method, interceptor)
})


export default http