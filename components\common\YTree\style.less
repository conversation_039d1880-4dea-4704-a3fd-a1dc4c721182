:deep(.ant-tree-list-holder-inner) {
    .ant-tree-treenode {
        width: 100%;
    }

    .ant-tree-treenode-selected {
        background-color: @acitve-background !important;

        .ant-tree-node-selected {
            color: var(--primary-color);

            .icon-yellow {
                color: var(--primary-color);
            }

            .handle_icon {
                display: inline-block;
            }
        }
    }

    .ant-tree-node-content-wrapper {
        background-color: transparent;
        flex: 1;
    }
    .ant-tree-title {
        display: flex;
    }
}
