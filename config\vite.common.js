import vue from '@vitejs/plugin-vue';
import unocss from 'unocss/vite';
import { resolve, basename } from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import vueSetupExtend from 'vite-plugin-vue-setup-extend';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { DynamicPublicDirectory } from 'vite-multiple-assets';

// 当前运行目录
const __runDirName = resolve(process.cwd());
const projectName = basename(__runDirName);

// $ 公共静态目录
const extraPublic = [`../../public/**`];

export const viteCommonConfig = () => {
  return {
    base: './',
    plugins: [
      vue({
        //TODO: https://github.com/nguyenbatranvan/vite-multiple-assets/issues/26
        // 采用了这个导致 在img标签引用相对路径不生效  "./..."
        // template: {
        //   transformAssetUrls: {
        //     img: [''],
        //   },
        // },
      }),
      vueJsx(),
      DynamicPublicDirectory(extraPublic),
      unocss(),
      vueSetupExtend(),
      AutoImport({
        dts: true,
        imports: ['vue', 'vue-router', 'pinia'],
        // $ 如果同名后面的会覆盖前面的
        dirs: [
          '../../utils',
          '../../hooks',
          'src/store',
          'src/utils',
          'src/hooks',
        ],
      }),
      Components({
        dirs: ['../../components', 'src/components'],
        resolvers: [
          AntDesignVueResolver({
            importStyle: false,
            resolveIcons: true,
          }),
        ],
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(__runDirName, './src'),
        // '@root': resolve(__runDirName, '../..'),
      },
    },
    server: {
      host: '0.0.0.0',
      port: 9008,
      warmup: {
        clientFiles: [
          "./src/components/**/*.*",
          "./src/store/**/*.*",
          "./src/router/**/*.*"
        ]
      }
    },
    build: {
      outDir: resolve(__runDirName, '../../dist', projectName),
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
      chunkSizeWarningLimit: 300,
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            'primary-color': '#00B781', // 全局主色
            'link-color': '#1890ff', // 链接色
            'success-color': '#00B781', // 成功色
            'warning-color': '#faad14', // 警告色
            'error-color': '#f5222d', // 错误色
            'font-size-base': '14px', // 主字号
            'heading-color': 'rgba(0, 0, 0, 0.85)', // 标题色
            'text-color': '#000000D9', // 主文本色
            'text-color-secondary': 'rgba(0, 0, 0, 0.45)', // 次文本色
            'disabled-color': 'rgba(0, 0, 0, 0.25)', // 失效色
            'suggestive-color': '#00000073', // 提示性文字
            'border-radius-base': '4px', // 组件/浮层圆角
            'border-color-base': '#d9d9d9', // 边框色
            'box-shadow-base': '0 2px 8px rgba(0, 0, 0, 0.15)', // 浮层阴影.
            'body-background': '#ffffff', // 白色
            'gray-background': '#F0F2F5FF', // 灰色背景
            'acitve-background': '#00B78114', // 选中颜色
            'picker-basic-cell-hover-with-range-color': '#dff7ec',
          },
          javascriptEnabled: true,
        },
      },
    },
  };
};
