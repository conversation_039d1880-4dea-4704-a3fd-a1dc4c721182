<template>
  <view class="container">
    <uni-forms :modelValue="state.formData" label-position="top" label-width="100">
      <template v-for="(item, idx) in questions" :key="idx">
        <!-- <RendeRuestion :data="item"/> -->
        <!-- 单选 -->
        <uni-forms-item v-if="item.quesTypeCode == 'SO' && showQues(item)">
          <template #label>
            <SO :data="item" :properties="properties" ref="quesRef"  @change="change" />
          </template>
        </uni-forms-item>
        <!-- 多选 -->
        <uni-forms-item v-if="item.quesTypeCode == 'MO' && showQues(item)" >
          <template #label>
            <MO :data="item" :properties="properties" ref="quesRef" @change="change" />
          </template>
        </uni-forms-item>
        <!-- 多选图片 -->
        <!-- 排序 -->
        <uni-forms-item v-if="item.quesTypeCode == 'RK' && showQues(item)" >
          <template #label>            
            <RK :data="item" v-model:value="item.options" @update="updateRk" :properties="properties" @change="change" ref="quesRef"/>
          </template>
        </uni-forms-item>
        <!-- 填空 -->
        <uni-forms-item v-if="item.quesTypeCode == 'FA' && showQues(item)">
          <template #label>
            <FA :data="item" :properties="properties" @change="change" ref="quesRef"/>
          </template>
        </uni-forms-item>
        <!-- 矩阵单选 -->
        <uni-forms-item v-if="item.quesTypeCode == 'MS' &&  showQues(item)">
          <template #label>
            <MS :data="item" :properties="properties" @change="change" ref="quesRef"/>
          </template>
        </uni-forms-item>
        <!-- 矩阵多选 -->
        <uni-forms-item v-if="item.quesTypeCode =='MM' && showQues(item)" :name="item.quesCode">
          <template #label>
            <MM :data="item" :properties="properties" @change="change" ref="quesRef"/>
          </template>
        </uni-forms-item>
      </template>
      <button v-if="questions.length" class="submit-btn" @click="submit">提交</button>
    </uni-forms>
    <view class="tip" v-if="errMsg">{{ errMsg }}</view>
  </view>
</template>

<script setup>
import { reactive, computed,ref,toRaw } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import { SO, MO, RK, FA, MS, MM } from "@/components/qtType"
import { getMoblieQuestionnaire, sendSubmitAnswer, getPreviewData } from "@/api"
import { examDecrypt } from '@/utils/crypto.js'
import { parseConfig } from "@/utils"

const quesRef = ref(null)

const state = reactive({
  formData: {},
  questions: [],
  properties: {},
  //跳题 满足条件才出现的题目, { show: 触发条件，hide：隐藏的题目}
  circumstanceList: [],
  opt: {},
  paperId: '',
  loading: true,
  errMsg: ''
})

function updateRk(e){
  console.log('---updateRk--',e)
}

const errMsg = computed(() => {  
  if (!state.questions.length && !state.loading) {
    return '暂无数据!'
  }
  return state.loading ? `加载中...` : state.errMsg
})

// 检查当前 满足条件才出现的题目
const examineCircumstance = (questions) => {
  // 单，多，排序
  const list = questions //.filter(i => ['SO', 'MO', 'RK'].includes(i.quesTypeCode))

  // 需要隐藏的题
  const result = list.reduce((arr, item) => {
    const needQt = item.options.filter(j => j.display).map(k => {
      const list = k.display.split(',') 
      return {
        show: k.optionNo,
        hide: list,
      } 
    })
    return arr.concat(needQt)
  }, [])

  console.log('需要隐藏的题目',result)
  state.circumstanceList = result
}

// 题目列表
const questions = computed(() => {
  // 处理互斥
  examineCircumstance(state.questions || [])    
  return state.questions
})

// 样式
const properties = computed(() => {
  for (let k in state.properties) {
    const obj = state.properties[k]
    if (Object.keys(obj)) {
      for (let j in obj) {
        const val = obj[j]
        if (typeof val == 'number') {
          obj[j] = val + 'px'
        }
      }
    }
  }
  return state.properties
})


// 是否渲染题目条件, 1.当前题隐藏
const showQues = computed(() => {
  return (item) => {
    // 是否隐藏题
    const needQt = state.circumstanceList.filter((i) => i.hide.includes(item.quesProperties?.quesMarking))
    const showList = needQt.map(i => i.show)  
    if(showList.length){
      // 当前题是隐藏题
      const allAnswer = Object.values(state.formData).flat().map(i => i.optionNo).filter(i => i)
      // 所有答案里面有没有当前答案
      const isShow = allAnswer.find(optionNo => showList.includes(optionNo))
      // console.log(isShow)
      return  isShow ? true :false
    }else{
      return true
    }
  }
})

const change = (value) => {
  // debugger
  for (let k in value) {
    state.formData[k] = value[k]
  }
  // console.log('_______',state.formData)
}

const submit = () => {
  if (window.self !== window.top || state.opt?.previewId) {
    return uni.showToast({
      title: '此问卷为预览状态，不能提交',
      icon: 'none'
    })
  } 

  // 1.是否存在填空错误
  const list = document.querySelectorAll('.fa-error')
  if(list.length){
    // const  quescode =  list[0].getAttribute('quescode')    
    list[0].parentNode?.scrollIntoView({behavior: "smooth"})
    console.log('填空验证错误')
    return uni.showToast({
      title: '请完成所有题目后提交',
      icon: 'none'
    })
  }  
  // 需要作答的题目
  let arr = []
  quesRef.value?.forEach(i=>{
    if(['MS','MM'].includes(i.quesTypeCode)){
      i.subQues.forEach(j=>{
        arr.push({
          quesCode:j,
          answer:toRaw(state.formData[j]),
          quesTypeCode:i.quesTypeCode
        })
      })
    }else{
      arr.push({
        quesCode:i.quesCode,
        answer:toRaw(state.formData[i.quesCode]),
        quesTypeCode:i.quesTypeCode
      })
    } 
  })
  // console.log('答案',arr)
  // 2.是否全部作答  
  const unanswered = arr.find(i=>i.answer==undefined)  
  if(unanswered){  
    let id =unanswered.quesCode
    if(['MS','MM'].includes(unanswered.quesTypeCode)){
       id = unanswered.quesCode?.split('-')[0]
    }
    document.getElementById(id)?.scrollIntoView({behavior: "smooth"})        
    console.log('有题未答',unanswered)
    return uni.showToast({
      title: '请完成所有题目后提交',
      icon: 'none'
    })
  }else{
    //全部有答案后   
    const fa = arr.find(i=>i.quesTypeCode=="FA") 
    if(fa?.answer.some(i=>i.text=='')){
        document.getElementById(fa.quesCode)?.scrollIntoView({behavior: "smooth"})    
        console.log('有填空未答',fa)
        return uni.showToast({
          title: '请完成所有题目后提交',
          icon: 'none'
        })
    }
  }

  uni.showLoading({
    title: '提交中...',
  });

  const { businessId, paperCode, identityCode, sectionCode, gradeCode, schoolId, scopeId, studentId, studentIdentity, special } = state.opt
  let params = {}

  const detailList = []
  try {
    for (let k in state.formData) {
      detailList.push({
        bankId: k,
        bankAnswer: state.formData[k].map(i => JSON.stringify(i))
      })
    }
  } catch (error) {
    console.log(error)
  }
  if (special === 'false') {
    params = {
      dataId: businessId,
      schoolId,
      scopeId,
      testType: paperCode,
      paperType: identityCode,
      paperId: state.paperId,
      detailList,
      sectionCode
    }
  } else {
    params = {
      dataId: businessId,
      schoolId,
      scopeId,
      testType: paperCode,
      paperType: identityCode,
      paperId: state.paperId,
      studentId,
      studentIdentity,
      detailList,
      sectionCode
    }
  }
  state.btnLoading = true

  console.log(params, '提交的答案');

  sendSubmitAnswer(params).then((res) => {
    uni.showToast({
      title: res.message || "操作成功",
      icon: 'none'
    });
    uni.redirectTo({ url: '/pages/prompt/index?idx=1' })
  }).catch((error) => {
    if (error?.data?.code == `**********`) {
      uni.redirectTo({ url: '/pages/prompt/index?idx=0' })
    }
  }).finally(() => {
    state.btnLoading = false
  })
}

async function initExam() {
  try {
    uni.showLoading({
      title: '正在加载...',
    })
    state.loading = true
    state.errMsg = ''
    const { businessId, paperCode, identityCode, sectionCode, gradeCode, schoolId, scopeId, personIdentity, personId, special } = state.opt
    const params = {
      identityCode,
      sectionCode,
      paperCode,
      gradeCode,
      businessId,
    }   
    
    const {data} = await getMoblieQuestionnaire(params)    
    if (data?.code == '1002028009') {
      state.loading = false
      state.errMsg = data.message
      return
    }
    if(data?.code=='**********'){
      // 链接已失效
      return uni.redirectTo({ url: '/pages/prompt/index?idx=0' })
    }

    // 解密
    const result = examDecrypt(data || '', `13246578123456781234567812345678`)

  

    if (result && typeof result == "string") {
      const json = JSON.parse(result)

      const { name, paperCode, properties, questions, paperId } = json || {}

      console.log(json)

      state.paperId = paperId
      // 解密完成 清除加载/ 如果解密出来的是个空数组 那说明没题目 之后的代码都不执行        
      if (!questions.length) {
        state.errMsg = `该问卷暂无题目`
        return uni.showToast({
          title: '该问卷暂无题目!',
          icon: 'none'
        })
      }
      // 全部总题数
      state.questions = questions

      state.properties = JSON.parse(properties || '{}')

      console.log(questions)

      uni.hideLoading()
      state.loading = false
    } else {
      state.loading = false
      uni.hideLoading()
      throw Error(`获取题目失败，解析异常`)
    }
  } catch (error) {
    uni.hideLoading()
    state.loading = false
    if (error?.data?.code == `1002028025`) {
      // 问卷未开始   
      return uni.redirectTo({ url: '/pages/prompt/index?idx=2' })
    }    

    if (error?.data?.code == `1002028026`) {
      // 问卷已结束
      return  uni.redirectTo({ url: '/pages/prompt/index?idx=3' })
    }

    console.log(error)
    state.errMsg = `获取题目失败，请稍后重试!`
    throw Error(`获取题目失败，api异常`)
  }
}

const initPreview = async (paperId,type) => {
  const { data } = await getPreviewData(paperId,type);
  if (data?.properties) {
    try {
      state.properties = JSON.parse(data.properties)
    } catch (error) {
      console.log(error)
    }
  }

  const arr = type=='question'? [data] : data?.questions

  state.questions = parseConfig(arr || []);
  console.log('预览试卷数据---', state.questions)
  state.loading = false
}


onLoad((opt) => {
  state.opt = opt
  // if (window.self !== window.top) {
  //   console.log('当前页面是在一个 iframe 中');
  //   opt.previewId && initPreview(opt.previewId)
  // } else {
  //   initExam()
  // }  
  if((window.self !== window.top) || opt.previewId) {
    opt.previewId && initPreview(opt.previewId,opt.type)
  }else{
    initExam()
  }
})

</script>

<style lang="scss" scoped>
.container {
  padding: 16px 16px 36px 16px;
  font-size: 14px;
  position: relative;
  min-height: 100vh;
  box-sizing: border-box;

  .tip {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
  }

  .submit-btn {
    border-radius: 5px;
    font-size: 16px;
    margin-top: 16px;

    &[disabled='true'] {
      background: #D9D9D9;
      color: #FFFFFF;

      &::after {
        content: "";
        border: none;
      }
    }
  }

}
</style>
