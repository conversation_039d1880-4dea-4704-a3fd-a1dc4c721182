<template>
  <view class="so-container" :id="data.quesCode">
    <view class="title" :style="properties.quesContentFontStyles">
      <view class="is-required" v-if="data.required" style="padding-top:3px;">
        <span>*</span>
      </view>
      <view class="idx">{{ data.quesNo }}.</view>
      <view v-html="data.title"></view>
    </view>   

    <template v-if="data.fsseQuesFiles.length">
      <view class="file-list" v-for="(item, idx) in data.fsseQuesFiles" :key="idx">		
          <free-audio :title="item.fileName" :audioId="item.id || item.fileUrl" :url='item.fileUrl'></free-audio>
      </view>
    </template>

    <radio-group class="op-list" @change="radioChange">
      <label class="op-cell" v-for="(item, index) in data.options" :key="index" :style="{
          width:`${100/data.maxRow}%`,        
        }">
					<view class="op-item">
						<radio :value="item.optionNo" style="transform: scale(0.7)" :activeBackgroundColor="themeColor"/>
            <text>{{ String.fromCharCode(65 + index) }}.</text>
					  <view :style="properties.optionFontStyles" class="op-text" v-html="item.optionContent"></view>
					</view>
          <template v-if="item.addText && state.check == item.optionNo"> 
            <uni-easyinput @input="inputChange" autoHeight type="textarea" maxlength="99" class="op-tip" trim="all" v-model="state.text" :placeholder="item.optionProperties?.addTextPlaceholder || '' " :clearable="false"></uni-easyinput>
          </template>  
          <image class="op-image" mode="aspectFit" v-if="item.optionProperties?.images && item.optionProperties.images[0]" :src="item.optionProperties.images[0]"></image>         

				</label>    

		</radio-group>
    <!--  -->    
  </view>
</template>

<script setup>
import { reactive } from 'vue';
import FreeAudio from '@/components/freeAudio'
import {debounce} from "@/utils"

const themeColor = getApp().globalData.themeColor;
const emit  = defineEmits(['update:value','change'])


const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  properties:{
    type: Object,
    default: () => {},
  },
  value:{
    type: [String,Number],
    default:'',
  }
});

const state = reactive({
  check:'',
  text:''
})

const radioChange = (e)=>{
  emit('update:value',e.detail.value)
  emit('change',{
    [props.data.quesCode]:[{
      optionNo:e.detail.value,
      text:state.text
    }]
  })
  state.check= e.detail.value  
}

const inputChange = debounce(function (e){
  emit('change',{
    [props.data.quesCode]:[{
      optionNo:state.check,
      text:state.text
    }]
  })
})

defineExpose({
  quesCode:props.data.quesCode,
  quesTypeCode:props.data.quesTypeCode
})

</script>

<style scoped lang="scss">
.so-container {
  .title{
    display: flex;
    // align-items: baseline;
    padding-bottom: 12px;
  }  
  .idx{
    padding-top:3px;
  }
 
  .op-list{
    display: flex;
    flex-wrap: wrap;
  }
  .op-cell{    
    .op-item{
      display: flex;   
      align-items: center;
    }  
    .op-tip{
      padding-left: 29px;
      font-size: 12px;
      color: #666666;
      max-width: 90%;
    }
    .op-image{
      max-width: 100%;
      width: 100px;     
      height: 100px;
      max-height: 400px;
      overflow: hidden;
    }  
  }

  
  :deep(.is-wrap) {
    display: flex;
    flex-direction: column;
  }
  :deep(.checklist-box) {
    margin: 10px 0;
  }
}
</style>
