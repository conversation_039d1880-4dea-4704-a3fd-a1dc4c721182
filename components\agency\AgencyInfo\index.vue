<template>
  <ContHeader type="back" />

  <div class="info">
    <div class="agency">
      <img
        v-if="state.details?.config?.logo"
        :src="state.details.config.logo"
        alt="logo"
      />
      <img v-else src="./logo-square.png" alt="" />
      <ul>
        <li>
          <p class="label">机构名称:</p>
          <p class="value ellipsis">{{ state.details.name || '-' }}</p>
        </li>
        <li>
          <p class="label">行政区域:</p>
          <p class="value ellipsis">{{ state.details.fullAreaName || '-' }}</p>
        </li>
        <li>
          <p class="label">联系人:</p>
          <p class="value ellipsis">{{ state.details.leader || '-' }}</p>
        </li>
        <li>
          <p class="label">手机号:</p>
          <p class="value ellipsis">{{ state.details.phone || '-' }}</p>
        </li>
        <li>
          <p class="label">超级账号:</p>
          <p class="value ellipsis">{{ state.details.phone || '-' }}</p>
        </li>
        <li>
          <p class="label">有效期终止时间:</p>
          <p class="value ellipsis">
            {{ state.details.expirationTime || '-' }}
          </p>
        </li>
        <li>
          <p class="label">允许发起监测:</p>
          <p class="value ellipsis">
            {{ state.details.isMonitor ? '是' : '否 ' }}
          </p>
        </li>
      </ul>
    </div>

    <div class="basic-info">
      <p class="title">基础信息</p>
      <ul>
        <li>
          <p class="label">办公地址:</p>
          <p class="value ellipsis">{{ state.details.address || '-' }}</p>
        </li>
        <li>
          <p class="label">受理时间:</p>
          <p class="value ellipsis">
            {{ state.details.acceptTime || '-' }}
          </p>
        </li>
        <li>
          <p class="label">联系电话:</p>
          <p class="value ellipsis">{{ state.details.officePhone || '-' }}</p>
        </li>
        <li>
          <p class="label">传真号码:</p>
          <p class="value ellipsis">{{ state.details.fax || '-' }}</p>
        </li>
        <li>
          <p class="label">电子邮箱:</p>
          <p class="value ellipsis">{{ state.details.email || '-' }}</p>
        </li>
        <li>
          <p class="label">通信地址:</p>
          <p class="value ellipsis">
            {{ state.details.contactAddress || '-' }}
          </p>
        </li>
        <li>
          <p class="label">邮政编码:</p>
          <p class="value ellipsis">{{ state.details.postalCode || '-' }}</p>
        </li>
      </ul>
    </div>

    <div class="statistics">
      <p class="title">监测统计</p>
      <ul>
        <li>
          <img src="./launch.png" alt="" />
          <div class="right">
            <p class="label">机构发起监测</p>
            <p class="value">
              {{
                (() => {
                  try {
                    return state.details.monitoringStatistics.initiatedInfo[0];
                  } catch (e) {
                    return 0;
                  }
                })()
              }}
            </p>
          </div>
        </li>
        <li>
          <img src="./running.png" alt="" />
          <div class="right">
            <p class="label">进行中</p>
            <p class="value">
              {{
                (() => {
                  try {
                    return state.details.monitoringStatistics.initiatedInfo[1];
                  } catch (e) {
                    return 0;
                  }
                })()
              }}
            </p>
          </div>
        </li>
        <li>
          <img src="./complete.png" alt="" />
          <div class="right">
            <p class="label">已完成</p>
            <p class="value">
              {{
                (() => {
                  try {
                    return state.details.monitoringStatistics.initiatedInfo[3];
                  } catch (e) {
                    return 0;
                  }
                })()
              }}
            </p>
          </div>
        </li>
        <li>
          <img src="./launch.png" alt="" />
          <div class="right">
            <p class="label">机构参与监测</p>
            <p class="value">
              {{
                (() => {
                  try {
                    return state.details.monitoringStatistics
                      .participationInfo[0];
                  } catch (e) {
                    return 0;
                  }
                })()
              }}
            </p>
          </div>
        </li>
        <li>
          <img src="./running.png" alt="" />
          <div class="right">
            <p class="label">进行中</p>
            <p class="value">
              {{
                (() => {
                  try {
                    return state.details.monitoringStatistics
                      .participationInfo[1];
                  } catch (e) {
                    return 0;
                  }
                })()
              }}
            </p>
          </div>
        </li>
        <li>
          <img src="./complete.png" alt="" />
          <div class="right">
            <p class="label">已完成</p>
            <p class="value">
              {{
                (() => {
                  try {
                    return state.details.monitoringStatistics
                      .participationInfo[3];
                  } catch (e) {
                    return 0;
                  }
                })()
              }}
            </p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
const route = useRoute();

const prose = defineProps({
  baseURL: {
    type: String,
    required: true,
    default: '/admin',
  },
});

// *********************
// Hooks Function
// *********************

const state = reactive({
  details: {},
});

// *********************
// Default Function
// *********************

const getDetails = async () => {
  const params = {
    id: route.query.id,
  };
  const { data } = await http.get(`${prose.baseURL}/org/details`, params);
  state.details = data;
};

// *********************
// Life Event Function
// *********************

getDetails();

// *********************
// Service Function
// *********************
</script>

<style lang="less" scoped>
.info {
  padding: 20px;
  .agency {
    display: flex;
    align-items: center;
    padding-bottom: 25px;
    border-bottom: 1px dashed #d9d9d9;
    img {
      width: 66px;
      height: 66px;
      margin-right: 24px;
      border-radius: 4px;
    }
    ul {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 24px;
      li {
        display: flex;
        align-items: center;
        overflow: hidden;
      }
    }

    .label {
      font-weight: 400;
      font-size: 14px;
      color: #595959;
      margin-right: 10px;
      text-wrap: nowrap;
    }

    .value {
      font-weight: 400;
      font-size: 14px;
      color: #262626;
    }
  }

  .title {
    position: relative;
    font-weight: 600;
    font-size: 14px;
    color: #262626;
    padding-left: 6px;
    margin-bottom: 16px;
    &::before {
      position: absolute;
      left: 0;
      transform: translateY(28%);
      content: '';
      width: 2px;
      height: 12px;
      background: var(--primary-color);
    }
  }

  .basic-info,
  .statistics {
    margin-top: 24px;

    ul {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 24px;
      li {
        display: flex;
        align-items: center;
        overflow: hidden;
      }
    }

    .label {
      font-weight: 400;
      font-size: 14px;
      color: #595959;
      margin-right: 10px;
      text-wrap: nowrap;
    }

    .value {
      font-weight: 400;
      font-size: 14px;
      color: #262626;
    }
  }

  .statistics {
    img {
      width: 50px;
      height: 50px;
      margin-right: 16px;
    }

    .label,
    .value {
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      line-height: 20px;
    }
    .value {
      margin-top: 10px;
    }
  }
}
</style>
