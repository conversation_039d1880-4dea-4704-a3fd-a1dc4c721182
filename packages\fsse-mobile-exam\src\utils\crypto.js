/*
 * @Author: 加密解密
 * @Date: 2021-09-17 19:02:51
 * @LastEditTime: 2022-08-05 11:11:55
 * @LastEditors: jingrou
 * @Description: In User Settings Edit
 * @FilePath: \cloud1\src\utils\cryptoJs.js
 */
import cryptoJs from "crypto-js";
// import { JSEncrypt } from "jsencrypt";
// import { Base64 } from "js-base64";

// 3DES 加密
// export const encryptByDES = (dataStr, key, iv) => {
//     const keyHex = cryptoJs.enc.Utf8.parse(key);
//     const encrypted = cryptoJs.TripleDES.encrypt(dataStr, keyHex, {
//         mode: cryptoJs.mode.CBC,
//         padding: cryptoJs.pad.Pkcs7,
//         iv: cryptoJs.enc.Utf8.parse(iv),
//     });
//     return encrypted.toString();
// };
// // 3DES 解密
// export const decryptByDES = (ciphertext, key, iv) => {
//     const keyHex = cryptoJs.enc.Utf8.parse(key);
//     const decrypted = cryptoJs.TripleDES.decrypt(
//         {
//             ciphertext: cryptoJs.enc.Base64.parse(ciphertext),
//         },
//         keyHex,
//         {
//             mode: cryptoJs.mode.CBC,
//             padding: cryptoJs.pad.Pkcs7,
//             iv: cryptoJs.enc.Utf8.parse(iv),
//         }
//     );
//     return decrypted.toString(cryptoJs.enc.Utf8);
// };

// function base64_decode_url_safe(string) {
//     var base64 = string.replace("-", "+").replace("_", "/");
//     var mod = base64.length % 4;
//     if (mod > 0) base64 += "====".substring(mod);
//     return Base64.decode(string || base64);
// }

//AES解密方法
export function Decrypts(word, keys) {
    let key = cryptoJs.enc.Utf8.parse(keys); //秘钥
    let result = cryptoJs.AES.decrypt(word, key, {
        mode: cryptoJs.mode.ECB,
        padding: cryptoJs.pad.Pkcs7,
    });
    let data = cryptoJs.enc.Utf8.stringify(result).toString();
    if (data) data = JSON.parse(data);
    return data;
}
// export function Encrypts(word, keys) {
//     let key = cryptoJs.enc.Utf8.parse(keys); //秘钥
//     let word = cryptoJs.enc.Utf8.parse(word);
//     let encrypted = cryptoJs.AES.encrypt(srcs, key, {
//         mode: cryptoJs.mode.ECB,
//         padding: cryptoJs.pad.Pkcs7,
//     })
//     return encrypted.toString();
// }


// 考试AES解密方法
export function examDecrypt(word, key, iv) {
    const aesKey = cryptoJs.enc.Utf8.parse(key) // 秘钥
    // const aesIv = cryptoJs.enc.Utf8.parse(iv)
    const result = cryptoJs.AES.decrypt(word, aesKey, {
        // iv: aesIv,
        mode: cryptoJs.mode.ECB,
        padding: cryptoJs.pad.Pkcs7
    })
    const info = cryptoJs.enc.Utf8.stringify(result).toString()
    return info
}


//AES解密方法
export function Decrypt(word, key, iv) {
    // console.log("data", word);
    var key = cryptoJs.enc.Utf8.parse(key); //秘钥
    // var iv = cryptoJs.enc.Utf8.parse(iv);
    var result = cryptoJs.AES.decrypt(word, key, {
        // iv: iv,/
        mode: cryptoJs.mode.ECB,
        padding: cryptoJs.pad.Pkcs7,
    });
    // var decryptedStr = result.toString(cryptoJs.enc.Utf8);
    var info = cryptoJs.enc.Utf8.stringify(result).toString();
    return info;
}
///AES加密方法
export function Encrypt(word, key, iv) {
    let srcs = cryptoJs.enc.Utf8.parse(word);
    let encrypted = cryptoJs.AES.encrypt(srcs, cryptoJs.enc.Utf8.parse(key), {
        iv: cryptoJs.enc.Utf8.parse(iv),
        mode: cryptoJs.mode.CBC,
        padding: cryptoJs.pad.Pkcs7,
    });
    // ciphertext：16进制加密，不加此方法就是Base64加密
    // return encrypted.ciphertext.toString().toUpperCase();
    return encrypted.toString();
}

export function random_string(len) {
    len = len || 32;
    let chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
    let maxPos = chars.length;
    let pwd = "";
    for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}

// const params = {
//   username: '18555237570',
//   password: '991015',
//   now: new Date().getTime()
// }

// console.log('加密前的请求数据', params)

// let str = encryptByDES(JSON.stringify(params), _key, _iv)

// console.log('加密后的请求数据', str)

// console.log('解密后的请求数据', decryptByDES(str, _key, _iv))
/**
 * 加密
 * @param string 加密的内容
 */
// let publicKey = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDRoTiKd8LeyrrF098wdIaLRxo1fOxZty2aG5LsJpmo6nSmBhi7guAMgYp6vNhu9It8WSGJEa+oVj7krdcHTfL0ga8VsyfuSzl/23u2IAnCKKLH3hCK5GONISlFJhZrSc7X7y5uJPBWc8Aj11wPaRCf5BoSWt9jjfd26X+G0A2e2wIDAQAB`; // 公钥
// let privateKey = `MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANGhOIp3wt7KusXT3zB0hotHGjV87Fm3LZobkuwmmajqdKYGGLuC4AyBinq82G70i3xZIYkRr6hWPuSt1wdN8vSBrxWzJ+5LOX/be7YgCcIoosfeEIrkY40hKUUmFmtJztfvLm4k8FZzwCPXXA9pEJ/kGhJa32ON93bpf4bQDZ7bAgMBAAECgYADUFUgezwtYW8KaYsBrRXjZcBYUB08XdTutrjf2sxbCznE1hvV+v+nsvSJdCrHtj3uPUp2bD0moBZmbwkTKHxKsdYjt5loK//he8owXryMyVoVPYJnKJ7yIktQuWBjgurAiHBA2gbe9Qh1ObZWMRemnII5jvHiTB8KmU7kNYQJwQJBAP9/X6To9eJzU0MJ5VxYZC2tGOFzqD0TGWUQ4wFY8BGBDDtRqY7O2FeNe6GraVZwxDnEn9dfpGLi6r/i9Pg2uekCQQDSCsF8q8C1242KsAvXO90EIoKb1gxohiJgzYGaFjgt3EOARTin/aqc1i0cGXHT5hvfgG1IVPAX+DMeXum2LBQjAkEAlXQsvoqHSq+rRMvFHv+VmiJnK3o8L328nSIDqAYeaB7UyN48EQ1t/8kDSM68D5RnNGOQHKwVvMKADz7zjT52AQJAPtUy3m2uv3hHVkaltCRXUIZWVfHpUldV72lqrMKa2bU7iCKs+SwcHsC/S9mcwIvkqK6NgobOMA1ylVJFTdbPywJBAIJmy5dI3oEXZ4cUJ46Qr+kMHGgtbpR7efyW1gNeHqMo9A0ZFYFwiNwYBmxQ4wJqyfgr5Juz1FcsOeGVjEHvZAU=`;
// export function publicKeys() {
//     return `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDRoTiKd8LeyrrF098wdIaLRxo1fOxZty2aG5LsJpmo6nSmBhi7guAMgYp6vNhu9It8WSGJEa+oVj7krdcHTfL0ga8VsyfuSzl/23u2IAnCKKLH3hCK5GONISlFJhZrSc7X7y5uJPBWc8Aj11wPaRCf5BoSWt9jjfd26X+G0A2e2wIDAQAB`;
// }
// export function encryptionJSE(string) {
//     let encrypt = new JSEncrypt({ b: 1024 });
//     encrypt.setPublicKey(publicKey);
//     let encrypted = encrypt.encrypt(string);
//     return encrypted;
// }

// 解密（一般不在前端解密，都是后端进行解密）
// export function decryptJSE(str) {
//     // debugger;
//     var decrypt = new JSEncrypt();

//     decrypt.setPrivateKey(privateKey);

//     let uncrypted = decrypt.decrypt(str);

//     return uncrypted;

// }
