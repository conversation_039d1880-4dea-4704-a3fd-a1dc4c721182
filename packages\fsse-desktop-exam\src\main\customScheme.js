
/**
 * 自定义协议处理模块
 * 该模块负责在Electron应用中创建和注册自定义的app://协议
 * 使应用能够通过app://路径加载本地资源，而不受跨域限制
 */

import { protocol } from "electron";
import fs from "fs";
import path from "path";

//为自定义的app协议提供特权
const schemeConfig = {
  standard: true,  // 遵循标准URL规则
  supportFetchAPI: true,  // 允许使用Fetch API访问
  bypassCSP: true,  // 绕过内容安全策略限制
  corsEnabled: true,  // 启用跨域资源共享
  stream: true  // 支持流式传输
};

/**
 * 注册app协议为特权协议
 * 这一步必须在app模块的ready事件前完成
 * 使自定义协议能够访问更多的API和功能
 */
protocol.registerSchemesAsPrivileged([
  { scheme: "app", privileges: schemeConfig }
]);

/**
 * 自定义协议处理类
 * 提供注册自定义协议和处理协议请求的功能
 */
export class CustomScheme {
  //根据文件扩展名获取mime-type
  /**
   * 根据文件扩展名确定对应的MIME类型
   * MIME类型用于在HTTP响应中指定内容类型，确保浏览器正确解析资源
   *
   * @param {string} extension - 文件扩展名，包含前导点，如'.js'、'.html'
   * @returns {string} 对应的MIME类型字符串
   */
  static getMimeType(extension) {
    let mimeType = "";
    if (extension === ".js") {
      mimeType = "text/javascript";
    } else if (extension === ".html") {
      mimeType = "text/html";
    } else if (extension === ".css") {
      mimeType = "text/css";
    } else if (extension === ".svg") {
      mimeType = "image/svg+xml";
    } else if (extension === ".json") {
      mimeType = "application/json";
    }
    return mimeType;
  }
  //注册自定义app协议
  /**
   * 注册自定义的app://协议处理器
   * 该方法将app://请求映射到本地文件系统，使Electron应用能够加载本地资源
   * 必须在应用ready事件后调用此方法
   *
   * @param {string} name - 默认HTML文件名，当请求没有指定文件扩展名时使用
   */
  static registerScheme(name) {
    protocol.registerStreamProtocol("app", (request, callback) => {
      // 从请求URL中提取路径部分
      let pathName = new URL(request.url).pathname;
      // 获取文件扩展名并转为小写
      let extension = path.extname(pathName).toLowerCase();

      // 如果没有扩展名，默认加载HTML文件
      if (extension == "") {
        pathName = name || "index.html";
        extension = ".html";
      }

      // 构建目标文件的完整路径
      let tarFile = path.join(__dirname, pathName);

      // 返回文件内容作为响应
      callback({
        statusCode: 200,  // HTTP状态码：成功
        headers: { "content-type": this.getMimeType(extension) },  // 设置正确的内容类型
        data: fs.createReadStream(tarFile),  // 创建文件读取流
      });
    });
  }
}