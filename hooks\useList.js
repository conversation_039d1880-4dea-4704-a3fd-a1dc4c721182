// 普通的数据结构
// const { page, query, getList } = useList('/api/list');

// 嵌套的数据结构
// const { page, query, getList } = useList('/api/list', {}, {
//   dataPath: 'monitoringRoomList'
// });

export default (url, other = {}, options = {}) => {
  const { dataPath = '' } = options;
  const query = reactive({
    pageNo: 1,
    pageSize: 10,
    ...other,
  });
  const page = reactive({
    // 加载状态
    loading: false,
    list: [],
    total: 0,
  });

  // 存储完整的响应数据
  const fullData = reactive({});

  const extractData = responseData => {
    if (!dataPath) return responseData;

    return dataPath.split('.').reduce((obj, key) => {
      return obj?.[key];
    }, responseData);
  };

  const getList = async params => {
    query.pageNo = 1;
    params && Object.assign(query, params);
    try {
      page.loading = true;
      const { data } = await http.post(url, query);
      // 保存完整数据
      Object.assign(fullData, data);

      const extractedData = extractData(data);
      if (extractedData) {
        Object.assign(page, extractedData);
      } else {
        console.warn('无法根据指定路径获取数据');
        Object.assign(page, data);
      }
    } catch (e) {
      console.error('异常', e);
    } finally {
      page.loading = false;
    }
    return Promise.resolve(true);
  };
  // 如果有默认搜索条件 op
  const reset = op => {
    const pageSize = query.pageSize;
    for (const key in query) {
      query[key] = null;
    }
    query.pageNo = 1;
    query.pageSize = pageSize;
    op && Object.assign(query, op);
    getList();
  };
  const paginationChange = val => {
    if (val.pageSize == query.pageSize) {
      getList(val);
    } else {
      getList({ pageSize: val.pageSize, pageNo: 1 });
    }
  };

  const updateByDelete = (len = 1) => {
    let pageNo = query.pageNo;
    if (page.list.length <= len) {
      // 当前页数完全删除
      pageNo = pageNo > 1 ? pageNo - 1 : 1;
    }
    getList({ pageNo });
  };

  return {
    page,
    query,
    getList,
    reset,
    paginationChange,
    updateByDelete,
    fullData, // 暴露完整数据
  };
};
