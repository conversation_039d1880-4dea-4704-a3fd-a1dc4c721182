/* 产引入jsencrypt实现数据RSA加密 */
// import JSEncrypt from 'jsencrypt' // 处理长文本数据时报错 jsencrypt.js Message too long for RSA
/* 产引入encryptlong实现数据RSA加密 */
import Encrypt from 'encryptlong'; // encryptlong是基于jsencrypt扩展的长文本分段加解密功能。

// 存储加密公钥密钥串
const publicKey = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFRz4l1i8aTI3inKGJWhhgWsJmv+fMlCazpZdAM7eeKEWf7yq4q3XpCZKykkOHZNJU29L6eePyetTkULhDRX7h3G76QnIwgKgguHFKTcOXT3WgTCzXHJXLqI83Bl6rHaWOytwvxB2pVjdyNfq9nxcuD0wmGTzbd6hgKkWZ3hAdswIDAQAB`;

export default {
  /* 加密 */
  encrypt(data) {
    var encryptor = new Encrypt();
    encryptor.setPublicKey(publicKey);
    // 如果是对象/数组的话，需要先JSON.stringify转换成字符串
    const result = encryptor.encryptLong(data);
    return result;
  },
  /* 解密 - PRIVATE_KEY - 验证 */
  decrypt(data) {
    var encryptor = new Encrypt();
    encryptor.setPrivateKey(publicKey);
    // 如果是对象/数组的话，需要先JSON.stringify转换成字符串
    var result = encryptor.decryptLong(data);
    return result;
  },
};
