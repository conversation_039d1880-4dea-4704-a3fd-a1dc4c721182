<template>
  <a-drawer
    :width="width"
    :closable="false"
    v-model:open="drawerOpen"
    :maskClosable="false"
    :keyboard="false"
    :mask="false"
    rootClassName="YDrawer"
    :destroyOnClose="true"
    :rootStyle="rootStyle"
    v-bind="$attrs"
    @close="close"
  >
    <template #title>
      <div class="drawer_title">
        <div>
          <ArrowLeftOutlined class="iconColor" @click="close" />
          <span class="ml-10">{{ title }}</span>
        </div>
        <slot name="title"></slot>
      </div>
    </template>
    <template #footer v-if="isFooterSlot">
      <slot name="footer"></slot>
    </template>
    <slot></slot>
  </a-drawer>
</template>
<script setup>
const slots = useSlots();

defineOptions({
  inheritAttrs: false,
});

// *********************
// Hooks Function
// *********************

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  rootStyle: {
    type: Object,
    default: () => {},
  },
  backMessage: {
    type: String,
    default: '',
  },
});

const urlParams = reactive({ ...getUrlParams() });

const emit = defineEmits(['update:open', 'close']);
let drawerOpen = ref(false);

// 是否有自定义插槽
const isFooterSlot = computed(() => {
  return slots.footer;
});

const width = computed({
  get: () => {
    return 'calc(100vw - 72px)';
  },
});

const rootStyle = computed(() => {
  // 是否是ifram嵌套
  const isIfram = !!urlParams.sysCode;
  const height = isIfram ? '100vh' : 'calc(100vh - 60px)';
  const top = isIfram ? '0px' : '60px';
  return {
    height,
    top,
    ...props.rootStyle,
  };
});

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const close = async () => {
  drawerOpen.value = false;
  emit('update:open', drawerOpen.value);
  emit('close');
};

// *********************
// Watch Function
// *********************

watch(
  () => props.open,
  val => {
    drawerOpen.value = val;
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="less">
.YDrawer .ant-drawer-content-wrapper {
  box-shadow: none;
  .ant-drawer-header {
    padding-top: 0px;
    padding-bottom: 0px;
    border-bottom: none;
  }
  .drawer_title {
    height: 58px;
    padding-left: 24px;
    margin-top: -4px;
    margin-left: -24px;
    margin-right: -24px;
    font-size: 16px;
    color: #000;
    font-weight: 600;
    border-bottom: 1px solid #d9d9d9;
    display: flex;
    align-items: center;
    flex: 1;
  }
}
.YDrawer {
  outline: none;
  .ant-drawer-content {
    border-radius: 25px 0 0 0;
  }
}

.iconColor {
  font-size: 16px;
  color: var(--primary-color);
}
</style>
