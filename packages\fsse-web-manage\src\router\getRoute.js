// import { routeConfigs } from './routes.config.js';

const store = useStore();
const modules = import.meta.glob('../pages/**/index.vue', {
  import: 'default',
});

// 隐藏路由名单
let Perms = [];
const setRouter = arr => {
  let list = [];
  arr?.forEach(i => {
    let item = {
      path: i.path,
      name: i.component,
      meta: {
        title: i.name,
        icon: i.icon,
        // 是否显示在左侧菜单中
        isTreeMenu: i.isTreeMenu,
      },
    };
    if (i.children && i.children.length > 0) {
      item.children = setRouter(i.children)[0];
    }
    if (i.filePath) {
      item.component = modules[i.filePath];
    }
    if (i.type == 'C') {
      list.push(item);
    }
  });
  return [list];
};
const setPerms = arr => {
  arr?.forEach(i => {
    if (i.perms) {
      Perms.push(i.perms);
    }
    setPerms(i.children || []);
  });
};

// !!!! 暂时不请求数据
let routerLists = null;
// let routerLists = [routeConfigs, []];

const whiteList = ['recommend', 'login'];
const isWhite = () => {
  const url = window.location.href;
  let flag = false;
  whiteList.forEach(i => {
    if (url.indexOf(i) != -1) {
      flag = true;
    }
  });
  return flag;
};
export default async () => {
  if (!store.accessToken) {
    return new Promise(r => {
      // window.location.href = '/#/login'
      r([[], []]);
    });
  } else {
    return new Promise(r => {
      if (routerLists) {
        r(routerLists);
      } else {
        http.post('/manage/menu/tree').then(async res => {
          const data = res.code !== 0 ? [] : res?.data;

          const routerList = setRouter(data);
          setPerms(data);
          store.Perms = Perms;
          routerLists = routerList;
          r(routerList);
        }).catch(error => {
          console.error('获取菜单路由失败:', error);
          // 即使获取菜单失败，也返回空数组，避免路由系统崩溃
          r([[], []]);
        });
      }
    });
  }
};
