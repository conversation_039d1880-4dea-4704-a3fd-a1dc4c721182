<template>

  <view class="ms-container" :id="data.quesCode">
    <view class="title" :style="properties.quesContentFontStyles">
			<text class="is-required" v-if="data.required">
				<span>*</span>
			</text>
			<text class="idx">{{ data.quesNo }}.</text>
			<view >
        <view style="display: inline-block;" v-html="data.title"></view>        
      </view>      
		</view>
    <!--  -->
    <template v-if="data.fsseQuesFiles.length">
      <view class="file-list" v-for="(item, idx) in data.fsseQuesFiles" :key="idx">		
          <free-audio :title="item.fileName" :audioId="item.id || item.fileUrl" :url='item.fileUrl'></free-audio>
      </view>
    </template>
    
    <!--  -->
    <view class="ms-content">
      <view class="header">
        <view class="row">
          <view class="col" v-for="(item,idx) in data.options" :key="idx">
            {{item.optionContent}}
          </view>          
        </view>
      </view>
      <!--  -->
      <view class="body">
        <view class="list">
          <view class="item" v-for="(item,idx) in data.subQues" :key="idx"> 
            <view class="th" :style="properties.optionFontStyles">{{item.title}}</view>
            <radio-group class="tr" @change="(e)=>radioChange(e,item)">
                <label class="td" v-for="(citem,jdx) in item.options" :key="jdx">   
                  <radio :value="citem.optionNo" :checked="state.checkedMap[item.quesCode] == citem.optionNo" style="transform: scale(0.8)" :activeBackgroundColor="themeColor"/>
                </label>    
              </radio-group>       
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive } from "vue"
import FreeAudio from '@/components/freeAudio'
const themeColor = getApp().globalData.themeColor;
const emit  = defineEmits(['change'])

const props = defineProps({
	data: {
		type: Object,
		default: () => { },
	},
	properties: {
		type: Object,
		default: () => { },
	},
});

const state = reactive({
  checkedMap:{

  }
})

function radioChange(evt,item){ 
  state.checkedMap[item.quesCode] = evt.detail.value
  const obj = {}
  for(let quesCode in state.checkedMap){   
    obj[quesCode] = [{
        optionNo:state.checkedMap[quesCode],       
      }]      
  }
  emit('change',obj)  
}

defineExpose({
  quesCode:props.data.quesCode,
  subQues:props.data.subQues.map(i=>i.quesCode),
  quesTypeCode:props.data.quesTypeCode
})

</script>

<style scoped lang="scss">
/* MS 组件样式 */
.ms-container {
  
  .title {
		display: flex;
		// align-items: flex-start;
		padding-bottom: 12px;
	}
  .idx{
    padding-top:3px;
  }

  .check-tip{
    font-size: 15px;
    color: #999999;
  }
  
  .ms-content{
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
  }

  .header {
    background: #f1f1f1;    
    padding: 8px 11px;
    .row {
      display: flex;
    }
    .col {
      flex: 1;      
    }
  }

  .body{
    .list{
      padding-top: 12px;
    }
    .item{
      .th{
        padding-left: 12px;
      }
      .tr{
          display: flex;
          align-items: center;
          padding-left: 12px;
          .td{
            flex: 1;
            padding: 12px 0;
          }
        }
    }
  }
}
</style>
