
<template>
  <router-view />
</template>

<script setup>
import { ipc<PERSON><PERSON><PERSON> } from "electron";

ipcRenderer.on("currentMac", (e, data) => {
  window.sessionStorage.setItem("currentMac", data);
});
ipcRenderer.on("console", (e, data) => {
  console.log(data);
});

window.onbeforeunload = (e) => {
  console.log("I do not want to be closed");  
  if(import.meta.env.MODE === 'development'){
    // 
  } else{
    e.returnValue = false;
  } 
};
</script>
<style lang="less">
:root {
  --primary-color: @primary-color;
  --link-color: @link-color;
  --success-color: @success-color; // 成功色
  --warning-color: @warning-color; // 警告色
  --error-color: @error-color; // 错误色
  --font-size-base: @font-size-base; // 主字号
  --heading-color: @heading-color; // 标题色
  --text-color: @text-color; // 主文本色，
  --suggestive-color: @suggestive-color; // 提示性文字

  --text-color-secondary: @text-color-secondary; // 次文本色
  --disabled-color: @disabled-color; // 失效色
  --border-radius-base: @border-radius-base; // 组件/浮层圆角
  --border-color-base: @border-color-base; // 边框色
  --box-shadow-base: @box-shadow-base; // 浮层阴影
  --body-background: @body-background; //白色
  --gray-background: @gray-background; //灰色背景
  --acitve-background: @acitve-background; //选中颜色
}
</style>
