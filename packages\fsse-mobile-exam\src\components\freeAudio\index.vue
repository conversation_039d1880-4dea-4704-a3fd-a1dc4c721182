<template>
	<!-- 音频播放器组件 -->
	<view v-if='url' class='audio-player'>
		<view class="control">
			<view class='play-box' @click='start(audioId)'>
				<view class="play" v-show='!status'></view>
				<view class="stop" v-show='status'></view>
			</view>
			<view class="current-time">{{ getTime(Math.round(currentTime)) }}</view>
			<view class="progress">								
				<slider @change='changeAudio' :min='0' :max='(+duration.toFixed(0)==0 ? 100 : +duration.toFixed(0)) ' :value='+currentTime.toFixed(0)'
					:step='0.1' class="slider" :activeColor="themeColor" backgroundColor="#0000000a" :block-color="themeColor"
					block-size="10"></slider>
			</view>
			<view class='total-time'>{{ getTime(Math.round(duration)) }}</view>
		</view>
	</view>
</template>

<script>

export default {
	data() {
		return {
			context: null,
			currentTime: 0,
			duration: 0,
			status: false,
			themeColor:getApp().globalData.themeColor
		}
	},
	props: {
		url: String,
		audioId: [String, Number]
	},
	created() {
		this.context = uni.createInnerAudioContext();
		this.context.src = this.url;
		this.onTimeUpdate();
		this.onCanplay();
		this.onEnded();
		uni.$on('stop', (id) => {
			if (id && id != this.audioId) {
				this.context.stop();
				this.status = false;
			} else if (!id) {
				this.context.stop();
				this.status = false;
			}
		})
	},
	methods: {
		start(id) { //点击播放
			let audioId = id;
			if (this.status) {
				this.context.pause();
				this.status = !this.status;
			} else {
				uni.$emit('stop', id)
				this.context.play()
				this.status = !this.status;
			}
		},
		onCanplay() { //进入可播放状态
			this.context.onCanplay(() => {
				this.context.duration;
				setTimeout(() => {
					this.duration = this.context.duration;
				}, 1000)
			})
		},
		onTimeUpdate() { //音频播放进度
			this.context.onTimeUpdate(() => {
				if (!Number.isFinite(this.context.duration)) {
					this.duration = this.context.currentTime;
					this.currentTime = this.context.currentTime;
				} else {
					this.duration = this.context.duration;
					this.currentTime = this.context.currentTime;
				}
			})
		},
		onEnded() { //播放结束
			this.context.onEnded(() => {
				this.status = false;
				this.currentTime = 0;
			})
		},
		changeAudio(e) {
			let paused = this.context.paused;
			this.context.pause();
			this.context.seek(e.detail.value)
			if (!paused) {
				this.context.play();
			}
		},
		getTime(time) {
			let m = parseInt(time / 60);
			let s = time % 60;
			return this.towNum(m) + ':' + this.towNum(s);
		},
		towNum(num) {
			if (num >= 10) {
				return num;
			} else {
				return '0' + num;
			}
		}
	}
}
</script>

<style scoped>

.audio-player {
    padding-bottom: 12px;
}
.audio {
	background: #EBFAF5;
	border-radius: 4px;
}

.control {
	background: #EBFAF5;
	border-radius: 4px;
	height: 32px;
	display: flex;
	align-items: center;
}

.play-box {
	cursor: pointer;
	margin: 0 8px;
}

.play-box {
	cursor: pointer;
	margin: 0 8px;

}

.play {
	width: 20px;
	height: 20px;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAvJJREFUWEfVmc9PE0EUxz+zXiSRKypXUBN/RBP/ATkJXFABSbx4N2IUPGgibbe9ConReDWa+AsMxpjQciDIyd9RI0mlKQkmWIVoFFtEEHZk2iyUUtidCmWdpMk28953Pvum897MVFBo6wlVIGQd0jqIMMpBlgPqo1oCRAJpJRDGa6R4RG3bcCFDCS2n3kAZc+I0cBTBXi1fySDwkE3yGocD42593QH2+7fw22gFzgNb3IqvYJea17jMZqudKlM9r9qcAcP+Y0jjOoKtTmJa/ZIxhHWKGrN7Nb+VAaUURII+wA84v4gW3YKxnNc2qfYFEUI9L2v5B+7sKKE0eRNoLGxcba8ukqUnOd4yleu5HDATuftFhLOZuqj2NeVGcjlg2FRTGtCOwdo4BKjxm9lSSwHVgsB4sI6/OafXkGA1ZC+cRUCVSqaM+JqvViek3H61ukusSjsFLQJu7NTmYi5MdQZQVQhLqFLkKgnXle0itKOKu58H6Rh5yrQ1pxsnJ/sUhqxQFScDGA6EQFxy8rL77+2vp2n7nvTX4V/fOfehl8fjMbfu7uwkIWr9vgxgj/lep7Z2HmigcdvuJQNFvsY5G+1laPKbOwAnK1W7a/37BOldiRV3ss/uzweo+v9IiysjzwkOD5CcndGRzG8rjUpBONgCsl1HbSVAW+PLdIoLsT5ufXpH3vrlejDRKugJ3EaIE659ACdAW+vZj1HORCO8nEjoyC/aSnlHRbAf5CEdBbeASlNF8MboWy7G+hifmdQZRtWLJ4KwOQTs1PHUAbR1J2anMeMDXP34gllpuR0upgCTbvOfrVoIoO3bPRal/k2XW8CUAvwJlLr1UHZFBEz+D1Ps9UWyzmmmORrh1T+mGa8naq+XOs9vFjKAQQRtblNN8bdbnt+wqtB5esuvAD1/aEpH0cvHTnuFbOxUOxzc0xs4r199pLcrXr48sqfa09dv2RnbsxeY2ZCevgLOBk1XHJqR4ojOQT8tsa6X6PmKdJH+hvgLOQGlYx4FDzkAAAAASUVORK5CYII=);
	background-repeat: no-repeat;
	background-size: 20px 20px;
}

.stop {
	width: 20px;
	height: 20px;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAoBJREFUWEfVmc1rE0EUwH9vvbTQeFOh6KnBi3ryH6gnkyD4gR/gxZsHUZDWo83uJlcrHsSDNw8VtKIIkq2n9h/wZD0o9aQE1FsTaC/u2NmQZrNJNjuxH9OBwMK89+a37+2892YijDpq1SlEXUSFZxFnEtQkoH961EHqqLCOOJ9Q8p7S3PdRlhIjpY/eUf7KXeAywmkjXcUq8I5D6innvd9ZdbMBLrsTbDqzwANgIqvxAXLNLRuPGAvnOefr59QxHDBwr6CcZwjHhhkzmlf8QsI7FP23aXqDAZUSliplwAWGv4gR3baw2rLtUyhXENHPPaP/wq8fj5NrvACujbausdYijdwtrs9sJDV7AVuee7WHcG2mRQrlG0lP9gIGvg6pZ+yDnVHwKLp+3FQ3oN4QOG928Zsb9hoKwqvxjdMB1Klkw1nb8d06DCk5r3f3eJhvp6AO4P6GNom5HeoWoK4QoehS9L9J2NRfg+SbOGpKV5wWYOBVQR5msV48kuf5qQuR6O0vHwj+rKWqmcrHMmSVkltuAdb8z1lr64/p+xwfOxyp/dxc58TKk1RAU/kY4Col94wQdSVhuhtiCKqgi0tnyFIlFdBUvsuYcvJCUJkBNZ8lvFrGdEFT+W4OmRVq3gIiN60EVOql9uAyqGkrAZEVIfC/AiftBOSbBmyY5D/Tb8pUPuGopgZcB3KWerBxEEJs+yY5AGnG9kRte6mzvlloAVYQ5rKkGtP2yVS+f7tlfcMaNa37eppLBi/R8utp6w9NkRdtPna2nby/oR5ycI9aZtuvPjSk1ZdH7VBbff0W3/TWXmDGIa2+Ao6DRhWHeyi5lPWg33UQ37VL9H5Feo/+hvgH0spxYwqyjEUAAAAASUVORK5CYII=);
	background-repeat: no-repeat;
	background-size: 20px 20px;
}

.current-time {
	font-size: 12px;
	color: rgba(0, 0, 0, 0.85);
}

.progress {
	flex: 1;
}

.slider {
	margin: 0 8px;
}

.total-time {
	font-size: 12px;
	color: rgba(0, 0, 0, 0.85);
	padding-right: 8px;
}
</style>
