import { defineConfig } from 'vitepress';

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: '监测项目-开发文档',
  description: '技术文档，解决方案等',
  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: 'Home', link: '/' },
      { text: '概述', link: '/README' },
      { text: '规范', link: '/standard/README' },
      { text: '项目集合', link: '/markdown-examples' },
      { text: '资源管理', link: '/https://www.baidu.com/' },
      { text: '技术方案', link: '/plan/README' },
    ],

    sidebar: [
      {
        text: '项目概述',
        link: '/README',
      },
      {
        text: '编码规范',
        link: '/standard/README',
      },
      {
        text: '项目集合',
        items: [
          { text: '后台管理', link: '/markdown-examples' },
          { text: '采集系统', link: '/api-examples' },
        ],
      },
      {
        text: '技术方案',
        link: '/plan/README',
        items: [{ text: '主题色', link: '/plan/themecolor' }],
      },
    ],
    socialLinks: [{ icon: 'github', link: 'https://git.yyide.com/fsse' }],
  },
});
