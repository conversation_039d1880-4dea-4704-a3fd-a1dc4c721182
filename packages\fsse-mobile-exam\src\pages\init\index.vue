<template>
    <div class="init_warp">
        <div class="content">


            <p class="title">{{ state.initData?.headerContent }}</p>
            <!-- <div class="honorific">{{ state.initData?.toSign }}</div>
            <div class="main">{{ state.initData?.toContent }}</div> -->

            <uni-forms :modelValue="formData" ref="formRef" :rules="rules">
                <uni-forms-item label="帐号：" name="username" class="form-item">
                    <input type="text" class="form-input" v-model="formData.username" placeholder="请输入帐号" />
                </uni-forms-item>
                <uni-forms-item label="密码：" name="password" class="form-item">
                    <input type="password" class="form-input" v-model="formData.password" placeholder="请输入密码" />
                </uni-forms-item>
            </uni-forms>


            <div class="but">
                <!-- <div class="next" @click="next"></div> -->
                <button :loading="state.loading" form-type="submit" style="width: 100%;color: #333;" @click="submit">登录</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
import { reactive, onMounted, ref } from "vue"
import { getInitPageData,submitLogin } from "@/api/index"
import { objectToQueryString } from "@/utils"

import RSA from "@/utils/rsa"
import {setToken} from '@/utils/auth'

const formRef = ref()

const rules = {
    username: {
        rules: [
            {
                required: true,
                errorMessage: '请输入帐号',
            }
        ],
        validateTrigger: 'submit'
    },
    password: {
        rules: [
            {
                required: true,
                errorMessage: '请输入密码',
            }
        ],
        validateTrigger: 'submit'
    }
}

const formData = reactive({
    username: '',
    password: ''
})

const state = reactive({
    initData: {
        headerContent: 'FSSE问卷调查',
        toContent: "",
        toSign: ""
    },
    opt: {},
    loading: false
})

function submit() {
    formRef.value.validate().then(res => {  
        uni.showLoading({
            title: '加载中...',
            icon: 'none'
        })
        const str = JSON.stringify({
            username: formData.username,
            password: formData.password
        })
        const encoded = RSA.encrypt(str)
        const { businessId, identityCode,sectionCode } = state.opt
        const p = {
            bid:businessId,
            userType:identityCode,
            encoded,
            sectionCode
        }
        submitLogin(p).then(res=>{
            if(res.data.code=='1020001085'){
                return uni.showToast({
                    title: res.data.message,
                    icon: 'none'
                })
            }
            const { accessToken } = res.data
            if(accessToken){
                setToken(accessToken)               
                next()
            }
        }).catch(err=>{
            uni.showToast({
                title: err.message || '登录失败',
                icon: 'none'
            })
        }).finally(()=>{
            uni.hideLoading()
        })
    }).catch(err => {
        console.log('表单错误信息：', err);
    })
}

async function init() {
    if (Object.keys(state.opt).length == 0) {
        return uni.showToast({
            title: '参数缺失,请联系管理员！',
            icon: 'error'
        })
    }

    const { businessId, paperCode, identityCode, sectionCode, gradeCode, schoolId, scopeId, personId, special } = state.opt
    try {
        // 获取起始页数据
        if (special === 'true') {
            const { code, data } = await getInitPageData('sampleEltern', businessId)
            state.initData = data
        } else {
            const { code, data } = await getInitPageData(identityCode, businessId)
            state.initData = data
            if (identityCode === 'eltern') {
                if (schoolId === '***********') {
                    state.initData.toContent = "受重庆市高新区公共服务局委托，开展对科学城明远未来小学、科学城明远未来中学办学情况进行调查，请您真实填写。我们只对群体数据作分析，𠄘诺对您个人信息保密。谢谢您！"
                }
            } else {
                if (schoolId === '***********') {
                    state.initData.toContent = "受重庆市高新区公共服务局委托，开展对科学城明远未来小学、科学城明远未来中学办学情况进行调查，请您真实填写。我们只对群体数据作分析，𠄘诺对您个人信息保密。谢谢您！"
                }
                if (schoolId === '***********') {
                    state.initData.toContent = "受重庆市高新区公共服务局委托，开展对名校长工作室、名师工作室完成任务情况进行调查，请您真实填写。我们只对群体数据作分析，𠄘诺对您个人信息保密。谢谢您！"
                }
            }
        }
    } catch (error) {
        if (error?.data?.code == `1002080005`) {
            // 链接过期了
            uni.redirectTo({ url: '/pages/prompt/index?idx=0' })
        }
    }
}


const next = () => {
    if (Object.keys(state.opt).length == 0) {
        return uni.showToast({
            title: '参数缺失,请联系管理员！',
            icon: 'error'
        })
    }

    const str = objectToQueryString(state.opt)
    console.log(str)
    if (state.opt.special === 'true') {
        // 进入验证学生信息页面
        uni.redirectTo({ url: `/pages/verify/index?${str}` })
    } else {
        // 直接进入答题界面            
        uni.redirectTo({ url: `/pages/exam/index?${str}` })
    }

}

onLoad((opt) => {
    console.log(opt)
    state.opt = opt
    window.localStorage.clear()
    init()
})

</script>

<style lang="scss" scoped>
.init_warp {
    position: relative;
    background: url("/static/init.png") no-repeat;
    background-size: cover;
    height: 100vh;
    color: #ffffff;

    .content {
        position: absolute;
        // margin: 0 50px;
        top: 280px;
        left: 25px;
        right: 25px;

        .title {
            font-size: 30px;
            margin: 15px 0;
        }

        .honorific {
            font-size: 20px;
            margin: 15px 0;
        }

        .main {
            line-height: 30px;
            font-size: 18px;
            text-indent: 28px;
        }

        .but {
            display: flex;
            margin: 28px auto 0;
            justify-content: center;
            flex: 1;

            .next {
                background: url("/static/nextstep.png") no-repeat;
                background-size: cover;
                width: 73px;
                height: 73px;
                transition: all .25s;

                &:active {
                    transform: scale(0.8);
                }
            }
        }
    }

    .form-item {
        background-color: #fff;
        color: #333;
        height: 46px;
        border-radius: 5px;
        padding-left: 12px;

        :deep(.uni-forms-item__label) {
            height: 100%;
        }

        .form-input {
            height: 100%;
        }
    }
}
</style>