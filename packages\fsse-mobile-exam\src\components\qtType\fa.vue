<template>
  <view class="fa-container" :id="data.quesCode">
    <!-- FA 填空 -->
    <view class="fa-title" :style="properties.quesContentFontStyles">
      <text class="is-required" v-if="data.required">
        <span>*</span>
      </text>
      <text class="idx">{{ data.quesNo }}.</text>
      <text class="fa-html" v-html="title"> </text>
    </view>

    <template v-if="data.fsseQuesFiles.length">
      <view class="file-list" v-for="(item, idx) in data.fsseQuesFiles" :key="idx">
        <free-audio :title="item.fileName" :audioId="item.id || item.fileUrl" :url='item.fileUrl'></free-audio>
      </view>
    </template>
    <!-- error -->    
    <view class="fa-error" v-if="state.errorMessage" :quescode="data.quesCode">
      <uni-icons type="clear" color="#ff4040" size="18"></uni-icons> 
      <text class="text">{{ state.errorMessage }}</text>
    </view>
  </view>
</template>

<script setup>
import { computed, nextTick, onMounted, reactive, render, h } from "vue"
import FreeAudio from '@/components/freeAudio'
// import Input from "@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput"

import DatePicker from "@dcloudio/uni-ui/lib/uni-datetime-picker/uni-datetime-picker"
import Input from "@/components/myInput"
import DatePickerCustom from "@/components/datePicker/index"
const themeColor = getApp().globalData.themeColor;
const props = defineProps({
  data: {
    type: Object,
    default: () => { },
  },
  properties: {
    type: Object,
    default: () => { },
  },
});
const emit = defineEmits(['change'])


const state = reactive({
  inputValues: {},
  errorMessage:'',  
})

const formatFillingContent = (content,quesCode) => {
  if (!content) return '';
  let index = 0;
  return content.replace(/_{6,}/g, match => {
    index++;
    return `<span class='filling-box ques-${quesCode}' quescode='${quesCode}' index='${index}' optionNo="${props.data.options[index - 1].optionNo}" faValidation="${props.data.options[index - 1].optionProperties.faValidation}" faMinValue="${props.data.options[index - 1].optionProperties.faMinValue}" faMaxValue="${props.data.options[index - 1].optionProperties.faMaxValue}">${match}</span>`;
  });
};

const title = computed(() => {
  try {
    return formatFillingContent(props.data.title,props.data.quesCode)
  } catch (error) {
    return props.data.title
  }
})

onMounted(() => {    
   // 组件配置映射
   const componentMap = {
      '文本': {
        type: Input,
        props: {
          tyep: 'text'
        }
      },
      '整数': {
        type: Input,
        props: {
          tyep: 'integer'
        },
      },
      '小数': {
        type: Input,
        props: {
          tyep: 'decimal'
        },
      },
      '年月': {
        type: DatePickerCustom,
        props: {
          fields: 'month'
        },
      },
      '日期': {
        // type: DatePicker,
        // props: {
        //   type: 'date',
        //   'clear-icon': false,
        //   'return-type': 'YYYY-MM-DD',
        //   placeholder: ' ',
        // },
        // className: ['fa-data-picker']
        type: DatePickerCustom,
        props: {
          fields: 'day'
        },        
      },
      '日期与时间': {
        type: DatePicker,
        props: {
          type: 'datetime',
          'clear-icon': false,
          format: 'YYYY-MM-DD HH:mm:ss',
          "return-type": 'YYYY-MM-DD HH:mm:ss',
          placeholder: ' ',
        },
        className: ['fa-data-pickerTime']
      },
      '时间': {
        type: DatePickerCustom,
        props: {
          mode: 'time'
        },
        // className: ['fa-data-picker', 'fa-time']
      },
    };

  nextTick(() => {     
    const createComponent = (inputItem, key) => {
      const validationType = inputItem.getAttribute('faValidation');
      const id = inputItem.getAttribute('optionno');
      const faMinValue = inputItem.getAttribute('faminvalue');
      const faMaxValue = inputItem.getAttribute('famaxvalue');    
      const quesCode = inputItem.getAttribute('quescode');
      
      
      const { type, props: compProps, className } = componentMap[validationType] || {
        type: Input,
        props: {
          placeholder: '请选择',
        },
      };
      compProps['id'] = id
      return h(type, {
        value: state.inputValues[key],
        class: className ?? [],
        'onUpdate:value': value => (state.inputValues[key] = value),
        onChange: value => {
          // console.log(state.inputValues)
          state.inputValues[key] = value

          let isValid = true;
          state.errorMessage = '';
          console.log(faMinValue,faMaxValue)
          // 根据不同的验证类型进行校验
          switch (validationType) {
            case '手机':
              if (!/^1[3-9]\d{9}$/.test(value)) {
                isValid = false;
                state.errorMessage =
                  '请输入正确的手机号码（如：18897194773）';
              }
              break;

            case '整数':
              if (!/^-?\d+$/.test(Number(value))) {
                isValid = false;
                state.errorMessage = '请输入正确的整数（如：12345678）';
              } else {
                const numValue = parseInt(value);
                if (faMinValue && numValue < Number(faMinValue)) {
                  isValid = false;
                  state.errorMessage = `最小值不能小于${faMinValue}(当前是${numValue})`;
                }
                if (faMaxValue && numValue > Number(faMaxValue)) {
                  isValid = false;
                  state.errorMessage = `最大值不能超过${faMaxValue}(当前是${numValue})`;
                }
              }
              break;

            case '小数':   
              if (!/^-?\d*\.?\d+$/.test(value)) {
                isValid = false;
                state.errorMessage =
                  '请输入正确的数字（如：12.34），请注意使用英文字符格式';
              } else {
                const numValue = Number.parseFloat(value);
                if (faMinValue && numValue < Number(faMinValue)) {
                  isValid = false;
                  state.errorMessage = `最小值不能小于${faMinValue}(当前是${numValue})`;
                }
                if (faMaxValue && numValue > Number(faMaxValue)) {
                  isValid = false;
                  state.errorMessage = `最大值不能超过${faMaxValue}(当前是${numValue})`;
                }
              }
              break;
            case '文本':
              const textLength = value.length;
              if (faMinValue && textLength < Number(faMinValue)) {
                isValid = false;
                state.errorMessage = `您的输入小于最小输入字数${faMinValue},当前字数为${textLength}`;
              }
              if (faMaxValue && textLength > Number(faMaxValue)) {
                isValid = false;
                state.errorMessage = `您的输入已超过最大字数${faMaxValue},当前字数为${textLength}`;
              }
              break;
            case '年月': 
            case '日期': 
            case '日期与时间':   
                if(faMinValue !='null' && faMaxValue!='null'){
                  if(+new Date(faMinValue) <=  +new Date(value) && +new Date(value) <= +new Date(faMaxValue)){

                  }else{
                    isValid = false;
                    state.errorMessage = `选择${validationType}范围须在${faMinValue}到${faMaxValue}区间`
                  }    
                }
                if(faMinValue!='null' && faMaxValue=='null'){
                  if(!(+new Date(value) >= +new Date(faMinValue))){
                    isValid = false;
                    state.errorMessage = `选择${validationType}范围须在${faMinValue}之后`
                  }
                }   
                if(faMinValue=='null' && faMaxValue!='null'){
                  if(!(+new Date(value) <= +new Date(faMaxValue))){
                    isValid = false;
                    state.errorMessage = `选择${validationType}范围须在${faMaxValue}之前`
                  }                
                }
              break;
            case '时间':
                if(faMinValue !='null' && faMaxValue!='null'){
                  if(+new Date(`2025-02-26 ${faMinValue}`) <=  +new Date(`2025-02-26 ${value}`) && +new Date(`2025-02-26 ${value}`) <= +new Date(`2025-02-26 ${faMaxValue}`)){

                  }else{
                    isValid = false;
                    state.errorMessage = `选择${validationType}范围须在${faMinValue}到${faMaxValue}区间`
                  }    
                }
                if(faMinValue!='null' && faMaxValue=='null'){
                  if(!(+new Date(`2025-02-26 ${value}`) >= +new Date(`2025-02-26 ${faMinValue}`))){
                    isValid = false;
                    state.errorMessage = `选择${validationType}范围须在${faMinValue}之后`
                  }
                }   
                if(faMinValue=='null' && faMaxValue!='null'){
                  if(!(+new Date(`2025-02-26 ${value}`) <= +new Date(`2025-02-26 ${faMaxValue}`))){
                    isValid = false;
                    state.errorMessage = `选择${validationType}范围须在${faMaxValue}之前`
                  }                
                }
              break;
          }

          // 如果验证通过，清空错误信息
          if (isValid) {
            state.errorMessage = '';
          }

          const arr = []
          for (let optionNo in state.inputValues) {
            arr.push({
              optionNo: optionNo,
              text: state.inputValues[optionNo] ?? ''
            })
          }
          const result = {
            [props.data.quesCode]: arr
          }          

          // debugger
          console.log('----',quesCode)

          emit('change', result)
        },
        ...compProps,
      });
    };
    // 
    Array.from(document.querySelectorAll(`.ques-${props.data.quesCode}`)).forEach(inputItem => {      
      const key = inputItem.getAttribute('optionNo');
      state.inputValues[key] ??= undefined;
      const container = document.createElement('span');
      container.style.display = 'inline-block';
      inputItem.parentNode.replaceChild(container, inputItem);
      render(createComponent(inputItem, key), container);
    });
  })
})

defineExpose({
  quesCode:props.data.quesCode,
  quesTypeCode:props.data.quesTypeCode
})


</script>

<style scoped lang="scss">
.fa-container {
  // font-size: 14px;
  // color: #606266;

  .fa-title {
    display: flex;
    align-items: baseline;
  }

  .file-list {
    padding-top: 12px;
  }

  .audio-player {
    padding-bottom: 0;
  }

  .fa-error {
    margin: 12px 0;
    background-color: #ffecec;
    padding: 8px 4px 8px 16px;
    color: #262626;
    display: flex;
    align-items: start;
    .text{
      padding-left: 4px;      
    }
  }

  .fa-html {
    :deep(p) {
      // display: flex;
      // align-items: center;
      // flex-wrap: wrap;
      white-space: pre-wrap;
    }
  }

  :deep(.op-input) {
    min-width: 58px;
    width: 58px;
    margin: 4px 8px;
  }

  :deep(.uni-easyinput__content-input) {
    height: 26px;

  }

  :deep(.fa-data-picker) {
    width: 100px;
    height: 26px;
    margin: 0 4px;

    .uni-date__x-input {
      height: 26px;
      line-height: 26px;
    }

    .uni-icons {
      display: none;
    }

  }

  :deep(.fa-data-pickerTime) {
    width: 160px;
    margin-top: 6px;
    margin-left: 4px;
    margin-right: 4px;
    .uni-date__x-input {
      height: 26px;
      line-height: 26px;
    }

    .uni-icons {
      display: none;
    }
  }

  :deep(.fa-time) {
    min-width: 60px;
  }

}

:deep(.uni-picker-container .uni-picker-action.uni-picker-action-confirm) {
  color: v-bind(themeColor)
}
</style>
