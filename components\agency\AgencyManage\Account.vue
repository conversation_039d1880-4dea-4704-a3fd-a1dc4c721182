<template>
  <YModal
    v-model:open="state.open"
    :width="408"
    title="账号管理"
    :footer="null"
  >
    <ul class="Account">
      <li>
        <p class="label">超级账号:</p>
        <p class="value">{{ state.record.phone }}</p>
      </li>
      <li>
        <p class="label">激活状态:</p>
        <p class="value">
          {{ IS_ACTIVE_MAP[state.record.isActive]?.text || '未激活' }}
        </p>
      </li>
    </ul>

    <footer>
      <a-button
        class="btn"
        type="primary"
        @click="resetPassword"
        :loading="state.loading"
      >
        重置密码
      </a-button>
    </footer>
  </YModal>
</template>

<script setup>
import { IS_ACTIVE_MAP } from './config';
const baseURL = inject('baseURL');
// *********************
// Hooks Function
// *********************

const state = reactive({
  open: false,
  formState: {
    phone: '',
  },
  record: {},
  loading: false,
});

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const resetPassword = async () => {
  try {
    state.loading = true;
    const res = await http.post(
      `${baseURL}/org/reset-password`,
      {},
      {
        orgId: state.record.id,
      }
    );
    YMessage.success(res.message);
    state.open = false;
  } finally {
    state.loading = false;
  }
};

// *********************
// DefineExpose Function
// *********************

const showModal = (record = {}) => {
  state.record = record;
  state.open = true;
};

defineExpose({ showModal });
</script>

<style lang="less" scoped>
.Account {
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 20px;
  padding: 24px;
  li {
    display: flex;
    align-items: center;
  }

  .label {
    font-weight: 400;
    font-size: 14px;
    color: #595959;
    margin-right: 12px;
  }

  .value {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }
}

footer {
  display: flex;
  justify-content: center;
  padding-bottom: 24px;

  .btn {
    width: 96px;
    height: 32px;
    background: var(--primary-color);
    border-radius: 100px;
  }
}
</style>
