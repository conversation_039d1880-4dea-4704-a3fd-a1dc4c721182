
import http from "@/utils/request";

export function submitLogin(data) {   
    return http.post(`/manage/questionnaireManagement/token`,data);
}
// 获取初始页数据
export function getInitPageData(type,id) {   
    return http.get(`/csq/common/obtainQuestionnaireCopyWriting?type=${type}&businessId=${id}`,{});
}

// 验证学生姓名
export function getFindStudent(data) {   
    return http.get(`/manage/questionnaireManagement/findStudent`,data)
}

// 提交答题
export function sendSubmitAnswer(data) {
    return http.post(`/csq/common/submit`,data);
}

// 获取考题
export function getMoblieQuestionnaire(data) {
    return http.post(`/csq/common/obtainQuestionnaire`,data);
}


// 获取预览数据
export function getPreviewData(paperId,type) {
   const url = type == 'question' ? `/paper/ques/v3/preview?id=${paperId}` : `/paper/paper/preview?paperId=${paperId}`
   return http.get(url, {});
}