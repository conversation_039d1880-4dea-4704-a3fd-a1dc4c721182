<template>
  <div class="rk-container" :id="data.quesCode">    
    <div class="title" :style="properties.quesContentFontStyles">
			<span class="is-required" v-if="data.required" style="padding-top:3px;">*</span>
			<span class="idx">{{ data.quesNo }}.</span>
			<span>
        <span  v-html="data.title"></span>
        <span style="display: inline-block;" class="check-tip">{{ tip }}</span> 
      </span>
		</div>
    <checkbox-group @change="checkChange"> 
      <l-drag
        class="ldrag"
        :list="value"
        @change="change"             
        :column="1"
        :gridHeight="gridHeight"
        handle
        :touchHandle="state.touchHandle"
      >
        <!-- // 每一项的插槽 grid 的 content 您传入的数据 -->
        <template #grid="{ active, content,index }">
          <!-- // grid.active 是否为当前拖拽项目 根据自己需要写样式 -->
          <view class="item" :style="{height:gridHeight}" :class="{ active: active }">
            <label class="left">
              <view class="op-title">
                <view class="idx">
                  <view class="checkbox-idx" :class="{active:state.checkList.includes(content.optionNo)}">
                    {{ index+1 }}
                  </view>
                  <checkbox                          
                    :checked="state.checkList.includes(content.optionNo)"          
                    :value="content.optionNo" 
                    style="transform: scale(0.7);display: none;"                 
                    :activeBackgroundColor="themeColor"
                    borderColor="#DCDFE6"
                    color="#fff"
                    :activeBorderColor="themeColor"                    
                    backgroundColor="#fff"/>                  
                </view>
                <view class="content" v-html="content.optionContent" :style="properties.optionFontStyles">                              
                </view>
              </view>

              <!-- -->
              <view v-if="content.addText  && state.checkList.includes(content.optionNo)" @click.stop @touchStart.stop>    
                <uni-easyinput @input="(e)=>inputChange(e,content.optionNo)" autoHeight type="textarea" maxlength="99" class="op-tip" trim="all" v-model="state.checkMap[content.optionNo]" :placeholder="content.optionProperties?.addTextPlaceholder || ''" :clearable="false"></uni-easyinput>
              </view>   
              <!--  -->
              <image class="op-image" mode="aspectFit" v-if="
                  content.optionProperties?.images && content.optionProperties.images[0]
                " :src="content.optionProperties.images[0]"></image>
            </label>
            <view v-if="state.checkList.length>1 && state.checkList.includes(content.optionNo)" class="move" @touchstart="state.touchHandle = true" @touchend="state.touchHandle = false" />
          </view>
        </template>      
      </l-drag>
    </checkbox-group>
  </div>
</template>

<script setup>
import { ref, reactive,watch, nextTick,computed } from 'vue';
import LDrag from '@/components/lime-drag/components/l-drag/l-drag.vue';
import {debounce} from "@/utils"
const themeColor = getApp().globalData.themeColor;
const emit = defineEmits(['change','update:value'])
const props = defineProps({
  value:{
    type:Array,
    default:()=>[]
  },
	data: {
		type: Object,
		default: () => { },
	},
	properties: {
		type: Object,
		default: () => { },
	},
});

const state = reactive({
  touchHandle:false,
  checkList:[],
  checkMap:{},
  exclusiveList:[]
})

const tip = computed(()=>{  
  if(props.data.minLength==0 && props.data.maxLength==0){
    return ``
  }  
  if(props.data.maxLength==0 && props.data.minLength>0){
    return `【最少选择${props.data.minLength}项,已选择${state.checkList.length}项】`
  } 
  return `【请选择${props.data.minLength}-${props.data.maxLength}项，已选择${state.checkList.length}项】`  
})

const gridHeight =  computed(()=>{
  const isExistImg =  props.data.options.some(i=>i.optionProperties.images && i.optionProperties.images[0])
  const isExistText = props.data.options.some(i=>i.addText) 

  if(isExistImg && isExistText){
    return '190px'
  }
  if(isExistImg && !isExistText){
     return '160px'
  } 
  return '58px'
})

const options = computed(()=>{
  // state.exclusiveList =  props.data.options.filter(i=>i.optionProperties.exclusive).map(i=>i.optionNo)     

  return props.data.options
})

// 拖拽后新的数据
const newList = ref([]);

const change = v => { 
  console.log(v)
  newList.value = v;
};

const verifyCheckExclus = (arr1,arr2)=>{
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const diff = [];
    for (const item of arr1) {
        if (!set2.has(item)) {
            diff.push(item);
        }
    }
    return diff
}

function reorderArrayBySort(originalArray, sortValues) {
    // 将数组分为两部分：匹配 sortValues 的部分和不匹配的部分
    const matched = originalArray.filter(item => sortValues.includes(item.sort));
    const unmatched = originalArray.filter(item => !sortValues.includes(item.sort));
    // 将匹配的部分放在前面，未匹配的部分放在后面
    return matched.concat(unmatched);
}


const checkChange = e => {
  // console.log(e)
  const arr = e.detail.value
  const currentSet = new Set(state.checkList);
  const diff = arr.filter(item => !currentSet.has(item));
  state.checkList = arr;  
  const idxList =[];

  state.checkList.forEach(optionNo => {      
    const item = options.value.find(item=>item.optionNo==optionNo)
    idxList.push(item.sort)
  });

  if(idxList.length){   
    emit('update:value',reorderArrayBySort(options.value,idxList))       
  }


  // console.log("新增的",diff)
  // 1. 如果当前题存在互斥，并且被选中，只留当前选中的。
  // if(state.exclusiveList.length && diff.length){    
  //   if(state.exclusiveList.includes(diff[0])){
  //     state.checkList = [diff[0]]
  //     return 
  //   }
  // }  
  // // 2. 如果已经选择了互斥的，之后选择不是互斥的，则留之后选择的
  // // 检查当前选中的是否存在互斥，返回不是互斥的
  // const ndiff =  verifyCheckExclus(arr,state.exclusiveList)     
  // if(ndiff.length){
  //   console.log('检查当前选中的是否存在互斥',ndiff)      
  //   state.checkList = [...ndiff]    
  // }
  // 3. 如果有最多选多少限制,超出则提示，新选的去掉
  const maxLength = props.data.maxLength
  const minLength = props.data.minLength  
  if(maxLength>0 && state.checkList.length > maxLength){   
    console.log('超出限制做多只能选择',maxLength)   
    uni.showToast({
      title: `此题最多只能选择${maxLength}项`,
      icon:"none",
      duration: 2000,
      success(){
        nextTick(()=>{
          state.checkList = state.checkList.filter(i=>i!=diff[0])

          const idxList =[];

          state.checkList.forEach(optionNo => {      
            const item = options.value.find(item=>item.optionNo==optionNo)
            idxList.push(item.sort)
          });
          if(idxList.length){   
            emit('update:value',reorderArrayBySort(options.value,idxList))       
          }

        })
      }
    });    
  } 
};


const inputChange = debounce(function (e,optionNo){
  emit('change',{
    [props.data.quesCode]:state.checkList.map(i=>{
      return {
        optionNo:i,
        text: state.checkMap[i] ?? ''
      }
    })
  })
})

watch(()=>state.checkList,debounce((list)=>{ 
  const arr =  list.map((optionNo)=>{
    return {
      optionNo,
      text:state.checkMap[optionNo] ?? ''
    }
  })
  emit('change',{
    [props.data.quesCode]:arr
  })  
},100),{
  deep:true
})

defineExpose({
  quesCode:props.data.quesCode,
  quesTypeCode:props.data.quesTypeCode
})

</script>

<style scoped lang="scss">
/* RK 组件样式 */
.rk-container {
  .title {
		display: flex;    
		// align-items: flex-start;    
		// padding-bottom: 12px;
	}
  .idx{
    // vertical-align: text-top;
    padding-top:3px;
  }

  .check-tip{
    font-size: 15px;
    color: #999999;
  }

  
  .checkbox-idx{
    border: 1px solid #d1d1d1;
    height: 17px;
    width: 17px;
    border-radius: 3px;
    margin-right: 5px;
    margin-left: 4px;
    background-color: rgb(255, 255, 255);
    border-color: rgb(220, 223, 230);
    text-align: center;
    line-height: 18px;
    color:#fff;
  }
  .checkbox-idx.active{
    background-color: v-bind(themeColor);
  }

  
  .item{
    height: 38px;
    display: flex;
    align-items: center;
    .left{
      flex: 1;
      

      .op-title{
        display: flex;
        align-items: center;
      }

      .op-tip{
        padding-left: 29px;
        font-size: 12px;
        color: #666666;
        width: 90%;
        margin-top: 12px;
      }

      .op-image {
        max-width: 100%;
        width: 100px;
        height: 100px;
        max-height: 400px;
        overflow: hidden;
        margin-left: 30px;
        margin-top: 12px;
      }
    }
    .move{
      width: 32rpx;
      height: 32rpx;
      padding-right: 12px;
      padding-left: 12px;
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAGVJREFUWEftlMENACAIA3VnxmBnXcCk6YPUx/mueDmBvcJnh99fAEgD3X1e31RVz7tuHgAM/G9gelFJAwDEDbibzc3LHnALunkAMCANxMcQgLgBd7G4edmEbkE3DwAGpIH4GE4DXJAQkCHjjguvAAAAAElFTkSuQmCC);
      background-size: 32rpx 32rpx;
      background-position: 12px;
      background-repeat: no-repeat;
    }
  }
  .active{
    background: #F5F5F5;
    border-radius: 4px;
  } 
}
</style>
