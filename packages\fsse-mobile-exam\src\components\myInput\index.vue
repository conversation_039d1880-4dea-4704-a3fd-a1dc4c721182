<template>
    <div :class="value?'fa-input__edit' :'fa-input__init'" :id="id" contenteditable="true">
        {{ value }}
    </div>
</template>

<script setup>
import {nextTick, watch} from "vue"
import {debounce} from "@/utils"

const props = defineProps({
    id:String,
    value:{
        type:[String,Number],
        default:''
    },
    type:{
        type:String,
        default:'text'
    }
})
const emit = defineEmits(['change','update:value'])

const change = debounce(function(e) {        
    emit('update:value', e.target.textContent)
    emit('change',e.target.textContent)                             
})

const addEventListener = (id)=>{
    nextTick(()=>{
      const divElement = document.getElementById(id);
      divElement.addEventListener('focus', function(e) {            
          e.target.className = 'fa-input__edit'                        
      });
      divElement.addEventListener('blur', function(e) {       
            if(!e.target.innerHTML){
                e.target.className = 'fa-input__init' 
            }                                 
      });
      divElement.addEventListener('input',change);
    })
}

watch(()=>props.id,(id)=>{    
    addEventListener(id)
},{
    immediate:true
})
</script>

<style lang="scss" scoped>   

    .fa-input__init{
        vertical-align: middle;
        padding: 0 3px;
        display: inline-block;
        margin: 4px 4px 4px 4px;
        min-width: 56px;
        height: 26px;
        line-height: 26px;
        background: #fff;
        border: 1px solid #e3e3e3;
        outline: none; 
        border-radius: 4px;
        cursor: pointer;       
    }
    .fa-input__edit{
        vertical-align: middle;
        padding-left: 20px;
        padding-right: 20px;
        display: inline-block;
        margin: 0 0px 4px 0;
        min-width: 20px;
        height: 26px;
        line-height: 26px;
        background: #fff;        
        outline: none;   
        position: relative;     
        &::before{
            content: "【";
            display: inline;
            margin-right: 4px;
            color: #e3e3e3;
            position: absolute;
            left: -2px;            
            top: 1px;
        }
        &::after{
            content: "】";
            display: inline;
            margin-left: 4px;
            color: #e3e3e3;
            position: absolute;
            right: -2px;            
            top: 1px;
        }
    }
</style>