<template>
  <div class="manage">
    <ContHeader />
    <div p-24>
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="getList()"
        @reset="reset({ $area: [] })"
      >
      </searchForm>
      <div flex flex-justify-end mt-20 mb-20>
        <a-button
          type="primary"
          @click="openAgency()"
          v-auth="'agency.manage.add'"
        >
          <div flex flex-items-center h-full>
            <i class="iconfont icon-icon-tianjia" />
            <span>新增</span>
          </div>
        </a-button>
        <a-button
          :disabled="!state.selectedRowKeys.length"
          @click="deleteAgency"
          v-auth="'agency.manage.del'"
        >
          删除
        </a-button>
      </div>

      <ETable
        hash="manage"
        :loading="page.loading"
        :columns="columns"
        :dataSource="page.list"
        :total="page.total"
        @paginationChange="paginationChange"
        :current="query.pageNo"
        :row-selection="{
          selectedRowKeys: state.selectedRowKeys,
          onChange: onSelectChange,
          getCheckboxProps: record => ({
            disabled: record.id === '2', // Column configuration not to be checked
          }),
        }"
      >
        <template #level="{ text }">
          {{ levelName(text) }}
        </template>
        <template #fullAreaNames="{ text }">
          {{ Array.isArray(text) ? text.join('-') : '' }}
        </template>

        <template #isMonitor="{ text }">
          {{ IS_MONITOR_MAP[text]?.text }}
        </template>

        <template #status="{ text }">
          <div class="status">
            <p
              class="status__dot"
              :style="{ background: STATUS_MAP[text]?.color }"
            ></p>
            <p>
              {{ STATUS_MAP[text]?.text }}
            </p>
          </div>
        </template>

        <template #isActive="{ text }">
          <span> {{ IS_ACTIVE_MAP[text]?.text }}</span>
        </template>

        <template #operate="{ record }">
          <a-button
            type="link"
            class="btn-link-color"
            @click="openAgency(record)"
            :disabled="record.id === '2'"
            v-auth="'agency.manage.edit'"
            >编辑</a-button
          >
          <!-- <a-button
            type="link"
            class="btn-link-color"
            @click="updateOrgStatus(record)"
            v-auth="'agency.manage.enable.disable'"
          >
            {{ record.status ? '禁用' : '启用' }}
          </a-button> -->
          <a-button
            type="link"
            class="btn-link-color"
            @click="openPersonalization(record)"
            v-auth="'agency.manage.personalization'"
            >个性化设置</a-button
          >
          <a-button
            type="link"
            class="btn-link-color"
            @click="jumpAgencyInfo(record)"
            v-auth="'agency.manage.organization.details'"
            >机构详情</a-button
          >
          <a-button
            type="link"
            class="btn-link-color"
            @click="openAccount(record)"
            v-auth="'agency.manage.account.management'"
            :disabled="record.id === '2'"
            >账号管理</a-button
          >
        </template>
      </ETable>
    </div>

    <NewAgency
      ref="newAgencyRef"
      :store="store"
      :system="system"
      :isAllow="isAllow"
      @confirm="getList({ pageNo: query.pageNo })"
    />

    <Account ref="accountRef" />
    <Personalization ref="personalizationRef" />
  </div>
</template>

<script setup>
import NewAgency from './NewAgency.vue';
import Account from './Account.vue';
import Personalization from './Personalization.vue';
import { computed, createVNode } from 'vue';
import { Modal, theme } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import {
  AGENCY_LEVEL,
  AGENCY_STATUS,
  IS_ACTIVE_STATUS,
  IS_MONITOR_MAP,
  IS_ACTIVE_MAP,
} from './config';

//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();

// 机构状态
const STATUS_MAP = {
  1: {
    color: token.value.colorPrimary,
    text: '已启用',
  },
  0: {
    color: '#FF3838',
    text: '未启用',
  },
};

const router = useRouter();

const props = defineProps({
  baseURL: {
    type: String,
    required: true,
    default: '/admin',
  },
  store: {
    type: Object,
    default: {},
  },
  system: {
    type: String,
    default: 'manage',
  },
  isAllow: {
    type: Boolean,
    default: true,
  },
});
provide('baseURL', props.baseURL);
const formList = [
  {
    type: 'input',
    value: 'name',
    label: '机构名称',
  },
  {
    type: 'select',
    value: 'level',
    label: '机构级别',
    list: AGENCY_LEVEL,
  },
  {
    type: 'region',
    // $后端需要的数据
    value: 'areaId',
    // $前端展示的数据
    showValue: '$area',
    label: '行政区域',
    span: 8,
    attrs: {
      baseURL: props.baseURL,
    },
  },
  {
    type: 'input',
    value: 'leader',
    label: '联系人',
  },
  {
    type: 'select',
    value: 'status',
    label: '机构状态',
    list: AGENCY_STATUS,
  },
  {
    type: 'select',
    value: 'isActive',
    label: '激活状态',
    list: IS_ACTIVE_STATUS,
  },
];

// *********************
// Hooks Function
// *********************

const newAgencyRef = ref(null);
const accountRef = ref(null);
const personalizationRef = ref(null);

const state = reactive({
  formState: {
    name: null,
    level: null,
    $area: [],
    areaId: null,
    leader: null,
    status: null,
    isActive: null,
  },
  selectedRowKeys: [],
});

const columns = computed(() => {
  return [
    { title: '机构名称', dataIndex: 'name' },
    { title: '行政区域', dataIndex: 'fullAreaNames' },
    { title: '机构级别', dataIndex: 'level' },
    { title: '联系人', dataIndex: 'leader' },
    { title: '手机号', dataIndex: 'phone' },
    { title: '允许发起监测', dataIndex: 'isMonitor' },
    { title: '机构状态', dataIndex: 'status' },
    { title: '激活状态', dataIndex: 'isActive' },
    { title: '有效期终止时间', dataIndex: 'expirationTime' },
    { title: '修改人', dataIndex: 'updateBy' },
    { title: '修改时间', dataIndex: 'updateTime' },
    { title: '操作', dataIndex: 'operate', width: 250 },
  ];
});

const levelName = computed(() => {
  return level => {
    return AGENCY_LEVEL.find(item => item.value == level)?.label;
  };
});

// *********************
// Default Function
// *********************

let { query, page, getList, reset, paginationChange, updateByDelete } = useList(
  `${props.baseURL}/org/page`,
  state.formState
);

// *********************
// Life Event Function
// *********************

getList();

// *********************
// Service Function
// *********************

// const updateOrgStatus = async record => {
//   const status = record.status ? 0 : 1;
//   const res = await http.post(`${props.baseURL}/org/update-status`, {
//     orgId: record.id,
//     status,
//   });
//   YMessage.success(res.message);
//   getList({ pageNo: query.pageNo });
// };

const openAccount = item => {
  accountRef.value.showModal(item);
};

const openAgency = item => {
  newAgencyRef.value.showModal(item);
};

const openPersonalization = item => {
  personalizationRef.value.showModal(item);
};

const onSelectChange = selectedRowKeys => {
  state.selectedRowKeys = selectedRowKeys;
};

const jumpAgencyInfo = item => {
  router.push({
    path: '/agency/info',
    query: {
      id: item.id,
    },
  });
};

const deleteAgency = () => {
  Modal.confirm({
    title: '删除',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否确认删除？`,
    okText: '确 定',
    cancelText: '取 消',
    onCancel() {},
    async onOk() {
      const res = await http.post(`${props.baseURL}/org/delete`, {
        ids: state.selectedRowKeys,
      });
      YMessage.success(res.message);
      state.selectedRowKeys = [];
      updateByDelete(state.selectedRowKeys.length);
    },
  });
};
</script>

<style lang="less" scoped>
.status {
  display: flex;
  align-items: center;

  .status__dot {
    width: 6px;
    height: 6px;
    border-radius: 6px;
    margin-right: 4px;
  }
}
</style>
