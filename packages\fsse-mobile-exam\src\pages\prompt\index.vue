<template>
    <view class="thank-participate">
        <view class="participate-center">
            <image width="227" height="212" :src="state.url" />
            <view>{{ state.title }}</view>
        </view>
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
import { reactive, } from 'vue'
import thankParticipate from '@/static/thankParticipate.png'
import linkHasExpired from '@/static/linkHasExpired.png'

const participate = [
    { title: '链接已失效', url: linkHasExpired },
    { title: '问卷已完成，感谢您的参与！', url: thankParticipate },
    { title: '问卷未开始！', url: linkHasExpired },
    { title: '问卷已结束！', url: linkHasExpired },
]

const state = reactive({
    title:'',
    url:''
})

onLoad((opt)=>{
    const idx = opt.idx || 0
    state.title = participate[idx] ? participate[idx].title : participate[0].title
    state.url = participate[idx] ? participate[idx].url : participate[0].url
})


</script>

<style scoped lang="scss">
.thank-participate {
    height: 100vh;

    .participate-center {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        text-align: center;
        margin-top: -142px;
    }

    p {
        font-size: 18px;
        text-align: center;
    }
}
</style>