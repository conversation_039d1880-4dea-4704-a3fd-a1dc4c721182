
<script>
export default {
  globalData: {
    previewData: {
      properties:{},
      questions:[]
    },
    parseDdataSuccess:true,
    themeColor: '#00b781'
  },    
  onLaunch: function () {
    console.log('App Launch');
    // const _this = this;
    // window.addEventListener(
    //   'message',
    //   e => {
    //     const { type, data } = e.data;
    //     if (type == 'preview' && data) {
    //       try {
           
    //         const result = JSON.parse(data);
    //         _this.globalData.previewData.questions = result.questions;
    //         _this.globalData.previewData.properties = JSON.parse(result.properties)
    //         _this.globalData.parseDdataSuccess = true
    //         _this.globalData.themeColor = result.themeColor || '#00b781'

    //         console.log('preview',result);

    //       } catch (error) {
    //         console.error('解析预览数据错误', error);
    //         _this.globalData.parseDdataSuccess = false
    //       }
    //     }
    //   },
    //   false
    // );
  },
  onShow: function () {
    console.log('App Show');
  },
  onHide: function () {
    console.log('App Hide');
  },
  mounted() {
    console.log('App mounted');
  },
};
</script>

<style lang="scss">
/*每个页面公共css */
:deep(.uni-picker-container .uni-picker-action.uni-picker-action-confirm){
    color:#00b781 !important;
}
:deep(.uni-datetime-picker--btn){
  background-color:#00b781 !important;
} 
:deep(.uni-calendar-item__weeks-box .uni-calendar-item--checked){
  background-color:#00b781 !important;
}

:deep(.uni-easyinput__content-textarea){
  min-height: 20px !important;
  line-height: 20px !important;
}
:deep(.is-focused){
  border-color:#00b781 !important;
}


.is-required {
  color: #dd524d;
  font-weight: bold;
}

.ellipsis {
    white-space: nowrap;     
    overflow: hidden;         
    text-overflow: ellipsis;   
}

:deep(.uni-picker-toggle){
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
</style>
