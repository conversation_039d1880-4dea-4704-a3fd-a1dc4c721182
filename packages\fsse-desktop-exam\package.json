{"name": "fsse-desktop-exam", "description": "考试端-桌面", "private": true, "version": "1.1.0", "scripts": {"dev": "vite", "build": "vite build", "build:uat": "cross-env NODE_ENV=uat esno ./scripts/publish.js uat", "build:uatrelease": "cross-env NODE_ENV=uatrelease esno ./scripts/publish.js uatrelease", "build:prod": "cross-env NODE_ENV=production esno ./scripts/publish.js", "release": "standard-version", "build-icon": "electron-icon-builder --input=./resources/fsse_logo.png --output=resources --flatten"}, "devDependencies": {"@vitejs/plugin-vue": "4.0.0", "chalk": "5.2.0", "cross-env": "7.0.3", "electron": "22.0.0", "electron-builder": "23.6.0", "electron-rebuild": "3.2.9", "electron-updater": "5.3.0", "esdk-obs-nodejs": "3.21.6", "esno": "0.16.3", "fs-extra": "11.1.0", "less": "4.1.3", "less-loader": "11.1.0", "pinia": "2.0.28", "semver": "7.3.8", "standard-version": "9.5.0", "unplugin-vue-components": "0.24.0", "vite": "4.0.0", "vite-plugin-optimizer": "1.4.2", "vite-plugin-style-import": "2.0.0", "vue": "3.2.45", "vue-router": "4"}, "dependencies": {"ant-design-vue": "3.2.15", "axios": "1.3.3", "consola": "2.15.3", "electron-log": "5.0.0-beta.16"}}