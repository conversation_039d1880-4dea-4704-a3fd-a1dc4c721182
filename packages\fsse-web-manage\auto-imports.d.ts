/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const RULE_MAP: typeof import('./src/utils/common.js')['RULE_MAP']
  const YMessage: typeof import('../../utils/YMessage.js')['default']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const arabicToChinese: typeof import('./src/utils/common.js')['arabicToChinese']
  const computed: typeof import('vue')['computed']
  const copyLink: typeof import('./src/utils/util.js')['copyLink']
  const createApp: typeof import('vue')['createApp']
  const createPinia: typeof import('pinia')['createPinia']
  const customCellFn: typeof import('./src/utils/util.js')['customCellFn']
  const customRef: typeof import('vue')['customRef']
  const debounce: typeof import('../../utils/common.js')['debounce']
  const deepClone: typeof import('../../utils/common.js')['deepClone']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const directive: typeof import('../../utils/directive.js')['default']
  const downloadFile: typeof import('../../utils/common.js')['downloadFile']
  const effectScope: typeof import('vue')['effectScope']
  const flatTrees: typeof import('./src/utils/util.js')['flatTrees']
  const generateRandomId: typeof import('../../utils/common.js')['generateRandomId']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getCurNodeParents: typeof import('./src/utils/util.js')['getCurNodeParents']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getDict: typeof import('../../utils/getDict.js')['default']
  const getRowSpanList: typeof import('./src/utils/util.js')['getRowSpanList']
  const getRowSpanListByFormatter: typeof import('./src/utils/util.js')['getRowSpanListByFormatter']
  const getTargetUrlParams: typeof import('../../utils/getTargetUrlParams.js')['getTargetUrlParams']
  const getTreeFirstfloorNodeParents: typeof import('./src/utils/util.js')['getTreeFirstfloorNodeParents']
  const getUploadFileHuaWeiOBS: typeof import('./src/utils/huaweiOBS.js')['getUploadFileHuaWeiOBS']
  const getUrlParams: typeof import('../../utils/common.js')['getUrlParams']
  const getWeekList: typeof import('./src/utils/util.js')['getWeekList']
  const h: typeof import('vue')['h']
  const http: typeof import('../../utils/http.js')['default']
  const inject: typeof import('vue')['inject']
  const isDisasterTicket: typeof import('../../utils/tencentCaptcha.js')['isDisasterTicket']
  const isEmpty: typeof import('../../utils/common.js')['isEmpty']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isValidCaptchaResult: typeof import('../../utils/tencentCaptcha.js')['isValidCaptchaResult']
  const makeup: typeof import('./src/utils/util.js')['makeup']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const numberToChineseNum: typeof import('./src/utils/common.js')['numberToChineseNum']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const pinia: typeof import('./src/store/pinia.js')['default']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const recursiveArray: typeof import('./src/utils/common.js')['recursiveArray']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const shiftNumber: typeof import('./src/utils/common.js')['shiftNumber']
  const showTencentCaptcha: typeof import('../../utils/tencentCaptcha.js')['showTencentCaptcha']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const testSetBtn: typeof import('./src/utils/util.js')['testSetBtn']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const upAxios: typeof import('./src/utils/upAxios.js')['default']
  const useAttrs: typeof import('vue')['useAttrs']
  const useColStore: typeof import('./src/store/useColStore.js')['default']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useExamIdStore: typeof import('./src/store/useExamIdStore.js')['default']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useList: typeof import('../../hooks/useList.js')['default']
  const useModel: typeof import('vue')['useModel']
  const useResize: typeof import('../../utils/common.js')['useResize']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useStore: typeof import('./src/store/index.js')['useStore']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const windowClear: typeof import('../../utils/common.js')['windowClear']
  const yConfirm: typeof import('../../utils/yConfirm.js')['default']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
