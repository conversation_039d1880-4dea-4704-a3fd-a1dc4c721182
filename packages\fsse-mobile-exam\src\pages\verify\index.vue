<template>
    <view class="init_warp">
        <view class="content">
            <view>
                <view v-if="state.isfail">
                    <view class="userName"><span class="must">*</span>学生姓名</view>
                    <view class="inpt">
                        <view :class="state.isfailerror ? 'inptFailBor' : 'inptBor'">
                            <input class="username" v-model="state.studentName" placeholder="请输入学生姓名" />
                        </view>                        
                        <view v-if="state.isfailerror" class="hint"><img src="/static/hint.png" alt="">
                            <span v-if="state.showName" class="hintText">请重新输入学生姓名</span>
                            <span v-if="state.noCorrect" class="hintText">请输入正确的学生姓名</span>
                        </view>
                    </view>
                    <view>
                        <button color='#FFFFFF' :loading="state.btnLoading"  block @click='verify'><span class="btnText">验证</span></button>
                    </view>
                </view>

                <div class="studentInfo" v-else>
                    <div>
                        <div v-if='state.studentInfo.length > 1'>
                            <radio-group class='van_radio_class'
                                @change="checkedSchool">
                                <label  v-for="(item, index) in state.studentInfo"
                                :key='item.studentId' class="radio-item">
                                    <radio :activeBackgroundColor="themeColor" :value="item.studentId">
                                        <div class="infoCard">
                                            <view style="margin: 16px 0;"><text>学校：</text> <text>{{ item.school || '-' }}</text></view>
                                            <view style="margin: 16px 0;"><text>班级：</text> <text>{{ item.grade || '-' }}</text></view>
                                            <view style="margin: 16px 0;"><text>姓名：</text> <text>{{ item.name || '-' }}</text></view>
                                            <view style="margin: 16px 0;"><text>性别：</text> <text>{{ item.gender || '-' }}</text></view>
                                        </div>
                                    </radio>
                                </label>
                            </radio-group>
                            <div class="passBtn">
                                <button @click='infoError' class="button"><span
                                        class="passBtnText">信息有误</span></button>
                                <button @click="infoPass" class="button"><span
                                        class="passBtnText">确认无误</span></button>
                            </div>
                        </div>
                        <div v-else>
                            <div class='studentInfo'>
                                <div v-for="(item, index) in state.studentInfo" :key='item.studentId'>
                                    <div class="oneinfoCard">
                                        <view style="margin: 16px 0;"><span>学校：</span> <span>{{ item.school || '-' }}</span></view>
                                        <view style="margin: 16px 0;"><span>班级：</span> <span>{{ item.grade || '-' }}</span></view>
                                        <view style="margin: 16px 0;"><span>姓名：</span> <span>{{ item.name || '-' }}</span></view>
                                        <view style="margin: 16px 0;"><span>性别：</span> <span>{{ item.gender || '-' }}</span></view>
                                    </div>
                                    <div class="passBtn">
                                        <button @click='infoError' class="button"><span
                                                class="passBtnText">信息有误</span></button>
                                        <button @click="oneInfoPass(item)" class="button"><span
                                                class="passBtnText">确认无误</span></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </view>
        </view>
    </view>
</template>
<script setup>

import { onLoad } from "@dcloudio/uni-app"
import { onMounted, reactive } from "vue"
import { getFindStudent } from "@/api"
const themeColor = getApp().globalData.themeColor;

import {objectToQueryString} from "@/utils"

const state = reactive({
    isfail: true,
    checkedStudentId: '',
    studentIdentity: '',
    isfailerror: false,
    showName: false,
    noCorrect: false,
    studentName: '',
    studentInfo: [],  
    opt:{},
    btnLoading:false
})

// 单个学生路由跳转
// const oneInfoPass = (item) => {
//     router.replace({
//         path: '/questionnaires/question', query: {
//             businessId,
//             paperCode,
//             identityCode,
//             sectionCode,
//             gradeCode,
//             schoolId,
//             scopeId,
//             studentId: item.studentId,
//             studentIdentity: item.studentIdentity,
//             special
//         }
//     })

// }

const infoPass = () => {
    if (!!state.checkedStudentId) {
        const { businessId,
                paperCode,
                identityCode,
                sectionCode,
                gradeCode,
                schoolId,
                scopeId, 
                special } = state.opt
        const obj = {
                businessId,
                paperCode,
                identityCode,
                sectionCode,
                gradeCode,
                schoolId,
                scopeId,
                studentId: state.checkedStudentId,
                studentIdentity: state.studentIdentity,
                special
            }
        const str = objectToQueryString(obj)
        uni.redirectTo({ url: `/pages/exam/index?${str}`})

    } else {
        uni.showToast({
            title: '请选择学生',
            icon: 'none'
        })
    }
}

const infoError = () => {
    state.checkedStudentId = ''
    state.studentIdentity = ''
    state.studentName = ''
    state.isfail = true
    state.showName = true
    state.isfailerror = false
    state.noCorrect = false
}

const checkedSchool = (e) => {
    const id = e.detail.value

    const studentIdentity = state.studentInfo.find(i=>i.studentId==id)?.studentIdentity    

    state.studentIdentity = studentIdentity

    state.checkedStudentId = id
}
const verify = () => {
    try {        
        const { businessId,schoolId} = state.opt
        let obj = {
            name: state.studentName,
            id: businessId,
            schoolId: schoolId
        }
        if(state.studentName=='' || state.studentName==undefined){
            state.isfailerror = true
            state.noCorrect = true
            state.showName = false
            return
        }

        state.btnLoading = true
        getFindStudent(obj)
            .then((res) => {                
                if(res.data.data==null && !Array.isArray(res.data)){
                    state.isfailerror = true
                    state.noCorrect = true
                    state.showName = false
                    return
                }
                state.studentInfo = res.data || [] 
                state.isfail = false      

                state.isfailerror = false
                state.noCorrect = false
                state.showName = false   
            }).catch((err) => {
                debugger
                if (err?.data?.code !== `0`) {
                    state.isfailerror = true
                    state.noCorrect = true
                    state.showName = false

                }
            }).finally(()=>{
                state.btnLoading = false
            })

    } catch (error) {
        console.log(error)
    }
}
onLoad((opt) => {
    state.opt = opt || {}
    console.log(opt)
})

</script>
<style lang="scss" scoped>
.init_warp {
    position: relative;
    background: url("@/static/init.png") no-repeat;
    background-size: cover;
    height: 100vh;
    overflow-y: auto;
    color: #ffffff;

    .content {
        position: absolute;
        // margin: 0 50px;
        top: 240px;
        left: 25px;
        right: 25px;

        .title {
            font-size: 30px;
            margin: 15px 0;
        }

        .studentInfo {
            font-size: 15px;
            padding-bottom: 50px;

            .infoCard {
                color: #000;
            }

         

            .passBtn {
                display: flex;
                padding-top: 6px;
                justify-content: space-between;

                .button {
                    width: 150px;
                    margin: 0;
                }

                .passBtnText {
                    color: #11C685;
                    font-size: 16px;
                }
            }

        }

        .userName {
            font-size: 20px;
            margin-bottom: 10px;

            .must {
                color: #FD4F45;
            }
        }

        .inpt {
            margin-bottom: 40px;
            .username{
                padding: 10px 16px;
                background-color: #fff;
                color: #333;
                input::placeholder{
                    font-size: 12px;
                }
            }

            .hint {
                font-size: 12px;
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .hintText {
                    color: #FD4F45;
                }
            }

            .inptBor {
                border-radius: 4px !important;
                border: 1px solid #EEEEEE !important;
            }

            .inptFailBor {
                border-radius: 8px !important;
                border: 1px solid #FD4F45 !important;
            }
        }

        .btnText {
            color: #11C685;
            font-size: 16px;
        }

    }
}

/* Avoid Chrome to see Safari hack */
@supports (-webkit-touch-callout: none) {
    .init_warp {
        /* The hack for Safari */
        height: -webkit-fill-available !important;
    }
}


.van_radio_class {
    width: 100%;
    display: flex;
    align-items: flex-start;
    flex-direction: column;

    .radio-item {
        padding: 0px 16px 0px 16px;
        margin-top: 10px;
        width: 90%;
        background: #f3fcf9;
        border-radius: 10px;
    }
}

.oneinfoCard {
    padding: 6px 16px 6px 16px;
    margin-top: 80px;
    width: 90%;
    background: #f3fcf9;
    border-radius: 10px;
    color: #000;
}
</style>