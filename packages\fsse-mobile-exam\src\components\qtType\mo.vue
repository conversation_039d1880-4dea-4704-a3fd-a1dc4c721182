<template>
	<div class="mo-container" :id="data.quesCode">
		<view class="title" :style="properties.quesContentFontStyles">
			<text class="is-required" v-if="data.required" style="padding-top:3px;">
				<span>*</span>
			</text>
			<text class="idx">{{ data.quesNo }}.</text>
			<view >
        <view style="display: inline-block;" v-html="data.title"></view>
        <text class="check-tip">{{ tip }}</text>
      </view>      
		</view>
    <!--  -->
		<template v-if="data.fsseQuesFiles">
			<view class="file-list" v-for="(item, idx) in data.fsseQuesFiles" :key="idx">				
        <free-audio :title="item.fileName" :audioId="item.id || item.fileUrl" :url='item.fileUrl'></free-audio>
			</view>
		</template>
    <!--  -->
		<checkbox-group class="op-list" @change="checkChange"> 
			<label class="op-cell" v-for="(item, index) in options" :key="index" :style="{
				width: `${100 / data.maxRow}%`,
			}">
				<view class="op-item">    

					<checkbox                          
                :checked="state.checkList.includes(item.optionNo)"          
                :value="item.optionNo" 
                style="transform: scale(0.7)"                 
                :activeBackgroundColor="themeColor"
                borderColor="#DCDFE6"
                color="#fff"
                :activeBorderColor="themeColor"
                backgroundColor="#fff"/>
          <text>{{ String.fromCharCode(65 + index) }}.</text>
					<view :style="properties.optionFontStyles" class="op-text" v-html="item.optionContent"></view>
				</view>
        <!--  -->
        <view v-if="item.addText && state.checkList.includes(item.optionNo)" @click.stop @touchStart.stop>
          <uni-easyinput @input="(e)=>inputChange(e,item.optionNo)" autoHeight type="textarea" maxlength="99" class="op-tip" trim="all" v-model="state.checkMap[item.optionNo]" :placeholder="item.optionProperties?.addTextPlaceholder || ''" :clearable="false"></uni-easyinput>
        </view>   
        <!--  -->
				<image class="op-image" mode="aspectFit" v-if="
					item.optionProperties?.images && item.optionProperties.images[0]
				" :src="item.optionProperties.images[0]"></image>
			</label>
		</checkbox-group>
		
	</div>
</template>

<script setup>
import { computed, nextTick, reactive, watch } from 'vue';
import FreeAudio from '@/components/freeAudio'
import {debounce} from "@/utils"

const themeColor = getApp().globalData.themeColor;
const props = defineProps({
	data: {
		type: Object,
		default: () => { },
	},
	properties: {
		type: Object,
		default: () => { },
	},
});
const emit  = defineEmits(['update:value','change'])

const state = reactive({
  checkList:[],
  // 
  checkMap:{},
  // 互斥集合
  exclusiveList:[]
});


const tip = computed(()=>{  
  if(props.data.minLength==0 && props.data.maxLength==0){
    return ``
  }  
  if(props.data.maxLength==0 && props.data.minLength>0){
    return `【最少选择${props.data.minLength}项,已选择${state.checkList.length}项】`
  } 
  return `【请选择${props.data.minLength}-${props.data.maxLength}项，已选择${state.checkList.length}项】`  
})


const options = computed(()=>{
  state.exclusiveList =  props.data.options.filter(i=>i.optionProperties.exclusive).map(i=>i.optionNo)    
  return props.data.options
})

const verifyCheckExclus = (arr1,arr2)=>{
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const diff = [];
    for (const item of arr1) {
        if (!set2.has(item)) {
            diff.push(item);
        }
    }
    return diff
}


const checkChange = e => {
  // console.log(e)
  const arr = e.detail.value

  const currentSet = new Set(state.checkList);
  const diff = arr.filter(item => !currentSet.has(item));
  state.checkList = arr;  
  // console.log("新增的",diff)
  // 1. 如果当前题存在互斥，并且被选中，只留当前选中的。
  if(state.exclusiveList.length && diff.length){    
    if(state.exclusiveList.includes(diff[0])){
      state.checkList = [diff[0]]
      return 
    }
  }  
  // 2. 如果已经选择了互斥的，之后选择不是互斥的，则留之后选择的
  // 检查当前选中的是否存在互斥，返回不是互斥的
  const ndiff =  verifyCheckExclus(arr,state.exclusiveList)     
  if(ndiff.length){
    // console.log('检查当前选中的是否存在互斥',ndiff)      
    state.checkList = [...ndiff]    
  }
  // 3. 如果有最多选多少限制,超出则提示，新选的去掉
  const maxLength = props.data.maxLength
  const minLength = props.data.minLength  

  

  if(maxLength>0 && state.checkList.length > maxLength){   
    console.log('超出限制做多只能选择',maxLength)   
    uni.showToast({
      title: `此题最多只能选择${maxLength}项`,
      icon:"none",
      duration: 2000,
      success(){
        nextTick(()=>{
          state.checkList = state.checkList.filter(i=>i!=diff[0])
        })
      }
    });    
  } 
};

const inputChange = debounce(function (text,optionNo){  
  // console.log(state.checkMap)
  
  emit('change',{
    [props.data.quesCode]:state.checkList.map(i=>{
      return {
        optionNo:i,
        text: state.checkMap[i] ?? ''
      }
    })
  })
})

watch(()=>state.checkList,debounce((list)=>{ 
  const arr =  list.map((optionNo)=>{
    return {
      optionNo,
      text:state.checkMap[optionNo] ?? ''
    }
  })
  emit('change',{
    [props.data.quesCode]:arr
  })  
},100),{
  deep:true
})

defineExpose({
  quesCode:props.data.quesCode,
  quesTypeCode:props.data.quesTypeCode
})

</script>

<style scoped>
/* MO 组件样式 */
.mo-container {
	.title {
		display: flex;
    /* align-items: flex-start; */
		padding-bottom: 12px;
	}
  .idx{
    padding-top:3px;
  }

  .check-tip{
    font-size: 15px;
    color: #999999;
  }

	.op-list {
		display: flex;
		flex-wrap: wrap;
	}

	.op-cell {
		.op-item {
			display: flex;
			align-items: center;
		}

    .op-tip{
      padding-left: 29px;
      font-size: 12px;
      color: #666666;
      max-width: 90%;
    }

		.op-image {
			max-width: 100%;
			width: 100px;
			height: 100px;
			max-height: 400px;
			overflow: hidden;
		}
	}

	.file-list {
		
	}

	:deep(.is-wrap) {
		display: flex;
		flex-direction: column;
	}

	:deep(.checklist-box) {
		margin: 10px 0;
	}
}
</style>
